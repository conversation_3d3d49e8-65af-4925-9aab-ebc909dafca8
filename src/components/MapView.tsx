import React, { useEffect, useRef, useState } from 'react';
import { View, StyleSheet, Text } from 'react-native';
import MapView, { <PERSON><PERSON>, PROVIDER_GOOGLE, LatLng, Callout } from 'react-native-maps';

type Location = {
    id: string;
    name: string;
    type: string;
    coordinate: LatLng;
};

const locations: Location[] = [
    { id: 'origin', name: '<PERSON>', type: 'Delivery', coordinate: { latitude: 43.4799, longitude: -110.7624 } },
    { id: 'dest1', name: '<PERSON>', type: 'Installation', coordinate: { latitude: 43.4820, longitude: -110.7605 } },
    { id: 'dest2', name: '<PERSON>', type: '<PERSON>air', coordinate: { latitude: 43.4770, longitude: -110.7650 } },
    { id: 'dest3', name: '<PERSON>', type: 'Delivery', coordinate: { latitude: 43.4855, longitude: -110.7692 } },
    { id: 'dest4', name: '<PERSON>', type: 'Delivery', coordinate: { latitude: 43.4908, longitude: -110.7555 } },
];

const MapViewComponent = () => {
    const mapRef = useRef<MapView>(null);
    const [selectedLocation, setSelectedLocation] = useState<Location | null>(null);

    useEffect(() => {
        const coords = locations.map(loc => loc.coordinate);
        mapRef.current?.fitToCoordinates(coords, {
            edgePadding: { top: 100, bottom: 100, left: 100, right: 100 },
            animated: true,
        });
    }, []);

    return (
        <View style={styles.container}>
            <MapView
                ref={mapRef}
                style={styles.map}
                provider={PROVIDER_GOOGLE}
                showsUserLocation={true}
            >
                {locations.map(location => {
                    return (
                        <Marker
                            key={location.id}
                            coordinate={location.coordinate}
                            onPress={() => setSelectedLocation(location)}
                        >

                            <Callout style={{ backgroundColor: 'white', width: 80, height: 40, alignItems: 'center' }}>
                                {/* <View> */}

                                <Text style={styles.name}>{location.name}</Text>
                                <Text>{location.type}</Text>
                                {/* </View> */}

                            </Callout>

                        </Marker>
                    )
                })}
            </MapView>
            {/* {selectedLocation && (
                <View style={styles.customCallout}>
                    <Text style={styles.name}>{selectedLocation.name}</Text>
                    <Text>{selectedLocation.type}</Text>
                </View>
            )} */}
        </View>
    );
};

export default MapViewComponent;

const styles = StyleSheet.create({
    container: { flex: 1 },
    map: { flex: 1 },
    name: { fontWeight: 'bold', marginBottom: 4 },
    customCallout: {
        position: 'absolute',
        top: 50,
        left: 20,
        backgroundColor: 'white',
        padding: 10,
        borderRadius: 5,
        borderWidth: 1,
        borderColor: '#ccc',
    },
});
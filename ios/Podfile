
 def node_require(script)
   # Resolve script with node to allow for hoisting
   require Pod::Executable.execute_command('node', ['-p',
     "require.resolve(
       '#{script}',
       {paths: [process.argv[1]]},
     )", __dir__]).strip
 end

# Use it to require both react-native's and this package's scripts:
 node_require('react-native/scripts/react_native_pods.rb')
 node_require('react-native-permissions/scripts/setup.rb')

platform :ios, min_ios_version_supported
prepare_react_native_project!

# ⬇️ uncomment the permissions you need
setup_permissions([
  # 'AppTrackingTransparency',
  # 'Bluetooth',
  'Calendars',
  # 'CalendarsWriteOnly',
  'Camera',
  # 'Contacts',
  'FaceID',
  'LocationAccuracy',
  'LocationAlways',
  'LocationWhenInUse',
  # 'MediaLibrary',
  # 'Microphone',
  # 'Motion',
  'Notifications',
  'PhotoLibrary',
  'PhotoLibraryAddOnly',
  # 'Reminders',
  # 'Siri',
  # 'SpeechRecognition',
  # 'StoreKit',
])

linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

target 'ARC' do
  config = use_native_modules!

  # Disable VisionCamera frame processors (optional setting)
  $VCEnableFrameProcessors = false
  
  use_react_native!(
    :path => config[:reactNativePath],
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  post_install do |installer|
    # React Native post install config
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false,
      # :ccache_enabled => true
    )
  
    # ENVFILE setup for react-native-config
    ENVFILES = {
      'Debug' => '$(PODS_ROOT)/../../.env',
      'Staging' => '$(PODS_ROOT)/../../.env.staging',
      'Release' => '$(PODS_ROOT)/../../.env.production',
    }

    # Add API domain configuration for each environment
    API_DOMAINS = {
      'Debug' => '$(REACT_NATIVE_CONFIG.API_URL)',
      'Staging' => '$(REACT_NATIVE_CONFIG.API_URL)',
      'Release' => '$(REACT_NATIVE_CONFIG.API_URL)',
    }
      
  
    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        if target.name == 'react-native-config'
          # config_name = config.name # "Debug", "Release", "Staging"
          config.build_settings['ENVFILE'] = ENVFILES[config.name]
        end

           # Add API domain to build settings
          config.build_settings['API_DOMAIN'] = API_DOMAINS[config.name]
      end
    end
  end

end

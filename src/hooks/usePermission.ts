import { useState, useEffect, useCallback } from 'react';
import { Platform, Linking, Alert } from 'react-native';
import { check, request, PERMISSIONS, RESULTS, Permission, checkNotifications, requestNotifications } from 'react-native-permissions';
import { useToast } from '@/hocs/toast';
import { useTranslation } from 'react-i18next';

// <PERSON><PERSON>c loại quyền mà hook hỗ trợ
export type PermissionType =
    | 'camera'
    | 'photoLibrary'
    | 'microphone'
    | 'location'
    | 'storage'
    | 'writeStorage'
    | 'storageAndWrite'
    | 'mediaImages'   // New for Android 13+
    | 'mediaVideo'    // New for Android 13+
    | 'mediaAudio'    // New for Android 13+
    | 'notification'; //


const permissionMap: Record<PermissionType, { ios: Permission, android: Permission }> = {
    camera: {
        ios: PERMISSIONS.IOS.CAMERA,
        android: PERMISSIONS.ANDROID.CAMERA
    },
    photoLibrary: {
        ios: PERMISSIONS.IOS.PHOTO_LIBRARY,
        android: PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE
    },
    microphone: {
        ios: PERMISSIONS.IOS.MICROPHONE,
        android: PERMISSIONS.ANDROID.RECORD_AUDIO
    },
    location: {
        ios: PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
        android: PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION
    },
    storage: {
        ios: PERMISSIONS.IOS.PHOTO_LIBRARY,
        android: PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE
    },
    writeStorage: {
        ios: PERMISSIONS.IOS.PHOTO_LIBRARY,
        android: PERMISSIONS.ANDROID.WRITE_EXTERNAL_STORAGE
    },
    storageAndWrite: {
        ios: PERMISSIONS.IOS.PHOTO_LIBRARY,
        android: PERMISSIONS.ANDROID.WRITE_EXTERNAL_STORAGE
    },
    mediaImages: {
        ios: PERMISSIONS.IOS.PHOTO_LIBRARY,
        android: PERMISSIONS.ANDROID.READ_MEDIA_IMAGES
    },
    mediaVideo: {
        ios: PERMISSIONS.IOS.PHOTO_LIBRARY,
        android: PERMISSIONS.ANDROID.READ_MEDIA_VIDEO
    },
    mediaAudio: {
        ios: PERMISSIONS.IOS.MEDIA_LIBRARY,
        android: PERMISSIONS.ANDROID.READ_MEDIA_AUDIO
    },
    notification: {
        ios: 'ios.permission.NOTIFICATIONS' as Permission,
        android: 'android.permission.POST_NOTIFICATIONS' as Permission,
    },
};


const permissionMessages: Record<PermissionType, { title: string, message: string }> = {
    camera: {
        title: 'Camera Access',
        message: 'The app needs camera access to take photos'
    },
    photoLibrary: {
        title: 'Photo Library Access',
        message: 'The app needs access to photo library to select images'
    },
    microphone: {
        title: 'Microphone Access',
        message: 'The app needs microphone access to record audio'
    },
    location: {
        title: 'Location Access',
        message: 'The app needs location access to provide better service'
    },
    storage: {
        title: 'Storage Read Access',
        message: 'The app needs read storage access for images and documents'
    },
    writeStorage: {
        title: 'Storage Write Access',
        message: 'The app needs write storage access to save images and documents'
    },
    storageAndWrite: {
        title: 'Storage Access',
        message: 'The app needs storage read and write access for images and documents'
    },
    mediaImages: {
        title: 'Images Access',
        message: 'The app needs access to images for display and storage'
    },
    mediaVideo: {
        title: 'Video Access',
        message: 'The app needs access to videos for playback and storage'
    },
    mediaAudio: {
        title: 'Audio Access',
        message: 'The app needs access to audio for playback and storage'
    },
    notification: {
        title: 'Notification Permission',
        message: 'The app needs permission to send notifications for important updates',
    },
};


export const getPermissionStatusText = (status: string | null): string => {
    switch (status) {
        case RESULTS.UNAVAILABLE:
            return 'Unavailable';
        case RESULTS.DENIED:
            return 'Denied';
        case RESULTS.GRANTED:
            return 'Granted';
        case RESULTS.LIMITED:
            return 'Limited';
        case RESULTS.BLOCKED:
            return 'Blocked';
        default:
            return 'Not checked';
    }
};

interface UsePermissionResult {
    status: string | null;
    isGranted: boolean;
    check: () => Promise<string | null>;
    request: () => Promise<boolean>;
    requestBothStoragePermissions?: () => Promise<boolean>;
    requestMediaPermissions?: () => Promise<boolean>;
    openSettings: () => Promise<void>;
    getStatusText: () => string;
}


const isAndroid13OrHigher = (): boolean => {
    return Platform.OS === 'android' && Platform.Version >= 33;
};

/**
 * Hook to manage permissions in the app
 * @param permissionType Type of permission to check/request
 * @param autoCheck Whether to automatically check permission when component mounts
 * @returns Object containing functions and states related to the permission
 */
export const usePermission = (
    permissionType: PermissionType,
    autoCheck: boolean = true
): UsePermissionResult => {
    const [status, setStatus] = useState<string | null>(null);
    const { showToast } = useToast();
    const { t } = useTranslation();


    const getPermission = useCallback(() => {

        if (isAndroid13OrHigher() && Platform.OS === 'android') {
            if (permissionType === 'storage' || permissionType === 'photoLibrary') {
                return PERMISSIONS.ANDROID.READ_MEDIA_IMAGES;
            } else if (permissionType === 'writeStorage' || permissionType === 'storageAndWrite') {
                return PERMISSIONS.ANDROID.READ_MEDIA_IMAGES;
            }
        }

        return Platform.select({
            ios: permissionMap[permissionType].ios,
            android: permissionMap[permissionType].android,
            default: permissionMap[permissionType].android,
        });
    }, [permissionType]);

    const permission = getPermission();


    const checkPermission = useCallback(async () => {
        try {

            if (Platform.OS === 'ios' && permissionType === 'notification') {
                const { status: notificationStatus } = await checkNotifications();
                setStatus(notificationStatus);
                return notificationStatus;
            }

            const result = await check(permission);
            setStatus(result);
            return result;
        } catch (error) {
            console.error(`Error checking ${permissionType} permission:`, error);
            return null;
        }
    }, [permission, permissionType]);


    const requestPermission = useCallback(async (): Promise<boolean> => {
        try {

            const currentStatus = await checkPermission();
            const isNewRequest = currentStatus !== RESULTS.GRANTED &&
                !(permissionType === 'photoLibrary' && currentStatus === RESULTS.LIMITED);
            if (Platform.OS === 'ios' && permissionType === 'notification') {
            }

            const result = await request(permission);
            setStatus(result);

            if ((result === RESULTS.GRANTED || (permissionType === 'photoLibrary' && result === RESULTS.LIMITED))) {
                if (isNewRequest) {
                    showToast('toast.permissionGranted', 'success');
                }
                return true;
            } else {
                if (isNewRequest) {
                    showToast('toast.permissionDenied', 'error');
                }
                return false;
            }
        } catch (error) {
            console.error(`Error requesting ${permissionType} permission:`, error);
            return false;
        }
    }, [permission, permissionType, showToast, checkPermission]);

    const requestBothStoragePermissions = useCallback(async (): Promise<boolean> => {
        // Kiểm tra trạng thái quyền hiện tại
        const currentStatus = await checkPermission();
        const isNewRequest = currentStatus !== RESULTS.GRANTED;

        if (Platform.OS === 'ios') {
            return requestPermission();
        }


        if (isAndroid13OrHigher()) {
            return requestMediaPermissions();
        }

        try {

            const readPermission = PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE;
            const writePermission = PERMISSIONS.ANDROID.WRITE_EXTERNAL_STORAGE;

            const results = await request(readPermission);
            const writeResults = await request(writePermission);


            const combinedResult = (results === RESULTS.GRANTED && writeResults === RESULTS.GRANTED)
                ? RESULTS.GRANTED
                : RESULTS.DENIED;

            setStatus(combinedResult);


            if (combinedResult === RESULTS.GRANTED) {
                if (isNewRequest) {
                    showToast('toast.permissionGranted', 'success');
                }
                return true;
            } else {
                if (isNewRequest) {
                    showToast('toast.permissionDenied', 'error');
                }
                if (results === RESULTS.BLOCKED || writeResults === RESULTS.BLOCKED) {
                    Alert.alert(
                        'Storage Access',
                        'The app needs read and write storage access to function properly. Please grant permission in device settings.',
                        [
                            { text: 'Close', style: 'cancel' },
                            { text: 'Open Settings', onPress: openSettings }
                        ]
                    );
                }

                return false;
            }
        } catch (error) {
            console.error('Error requesting storage permissions:', error);
            return false;
        }
    }, [requestPermission, showToast, checkPermission]);

    const requestMediaPermissions = useCallback(async (): Promise<boolean> => {
        const currentStatus = await checkPermission();
        const isNewRequest = currentStatus !== RESULTS.GRANTED;

        if (Platform.OS === 'ios') {
            return requestPermission();
        }


        if (!isAndroid13OrHigher()) {
            return requestBothStoragePermissions();
        }

        try {

            const imagePermission = PERMISSIONS.ANDROID.READ_MEDIA_IMAGES;
            const videoPermission = PERMISSIONS.ANDROID.READ_MEDIA_VIDEO;

            const imageResult = await request(imagePermission);
            const videoResult = await request(videoPermission);


            setStatus(imageResult);


            if (imageResult === RESULTS.GRANTED) {
                if (isNewRequest) {
                    showToast('toast.permissionGranted', 'success');
                }
                return true;
            } else {
                if (isNewRequest) {
                    showToast('toast.permissionDenied', 'error');
                }
                if (imageResult === RESULTS.BLOCKED) {
                    Alert.alert(
                        'Media Access',
                        'The app needs access to images and videos to function properly. Please grant permission in device settings.',
                        [
                            { text: 'Close', style: 'cancel' },
                            { text: 'Open Settings', onPress: openSettings }
                        ]
                    );
                }

                return false;
            }
        } catch (error) {
            console.error('Error requesting media permissions:', error);
            return false;
        }
    }, [requestPermission, requestBothStoragePermissions, showToast, checkPermission]);

    const openSettings = useCallback(async () => {
        try {
            await Linking.openSettings();
        } catch (error) {
            console.error('Error opening settings:', error);
            showToast('toast.settingsOpenError', 'error');
        }
    }, [showToast]);


    const getStatusText = useCallback(() => {
        return getPermissionStatusText(status);
    }, [status]);


    const isGranted = status === RESULTS.GRANTED ||
        (permissionType === 'photoLibrary' && status === RESULTS.LIMITED);


    useEffect(() => {
        if (autoCheck) {
            checkPermission();
        }
    }, [autoCheck, checkPermission]);


    const result: UsePermissionResult = {
        status,
        isGranted,
        check: checkPermission,
        request: requestPermission,
        openSettings,
        getStatusText,
    };


    if (permissionType === 'storage' ||
        permissionType === 'writeStorage' ||
        permissionType === 'storageAndWrite' ||
        permissionType === 'photoLibrary') {
        result.requestBothStoragePermissions = requestBothStoragePermissions;
        result.requestMediaPermissions = requestMediaPermissions;
    }

    return result;
};

export default usePermission; 
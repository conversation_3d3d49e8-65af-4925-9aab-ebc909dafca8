import React from 'react';
import { Image, StyleSheet, View, ImageSourcePropType, Text } from 'react-native';
import { BORDER_RADIUS, COLORS, FONT_SIZE, FONT_WEIGHT } from '@/constants/UI/themes';
import { getInitials } from '@/utils/text';
import { getResponsiveFontSize } from '@/constants/UI/responsive';
interface ProfileAvatarProps {
    source?: ImageSourcePropType;
    name?: string;
    style?: object;
}

const ProfileAvatar: React.FC<ProfileAvatarProps> = ({ source, name, style }) => {


    return (
        <View style={[styles.container, style]}>
            <Text style={styles.initials}>
                {getInitials(name)}
            </Text>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        width: FONT_SIZE.size64,
        height: FONT_SIZE.size64,
        borderRadius: BORDER_RADIUS.round,
        overflow: 'hidden',
        justifyContent: 'center',
        alignItems: 'center',
    },
    image: {
        width: '100%',
        height: '100%',
    },
    initials: {
        fontSize: getResponsiveFontSize(FONT_SIZE.size22),
        fontWeight: FONT_WEIGHT.regular,
        color: COLORS.light.primary,
    },
});

export default ProfileAvatar; 
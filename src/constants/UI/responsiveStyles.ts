import { StyleSheet, TextStyle, ViewStyle } from 'react-native';
import { useWindowDimensions } from 'react-native';
import { useMemo } from 'react';
import {
    getBreakpoint,
    adjustSpacing,
    isAndroid,
    RESPONSIVE_FONT_SIZE,
    RESPONSIVE_HEADER_HEIGHT
} from './responsive';
import { SHADOWS, SPACING, FONT_FAMILY, FONT_WEIGHT, BORDER_RADIUS, BUTTON_HEIGHT, LINE_HEIGHT, INPUT_HEIGHT } from './themes';
import { DeviceType } from '@/types';

export const BASE_STYLE = StyleSheet.create({
    flex: {
        flex: 1,
    },
    flexGrow: {
        flexGrow: 1,
    },
    row: {
        flexDirection: 'row',
    },
    rowCenter: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    column: {
        flexDirection: 'column',
    },
    rowSpaceBetween: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    centerContent: {
        justifyContent: 'center',
        alignItems: 'center',
    },
    spaceBetween: {
        justifyContent: 'space-between',
    },
    spaceAround: {
        justifyContent: 'space-around',
    },
    justifyStart: {
        justifyContent: 'flex-start',
    },
    justifyEnd: {
        justifyContent: 'flex-end',
    },
    alignStart: {
        alignItems: 'flex-start',
    },
    alignEnd: {
        alignItems: 'flex-end',
    },
    wrap: {
        flexWrap: 'wrap',
    },
    absolute: {
        position: 'absolute',
    },
    relative: {
        position: 'relative',
    },
    fullWidth: {
        width: '100%',
    },
    fullHeight: {
        height: '100%',
    },
});

// Function to generate responsive common styles
export const getCommonStyles = (breakpoint: DeviceType = getBreakpoint()) => {
    return StyleSheet.create({
        button: {
            ...BASE_STYLE.centerContent,
            height: isAndroid ? BUTTON_HEIGHT * 0.9 : BUTTON_HEIGHT,
            paddingHorizontal: adjustSpacing(SPACING.md),
            borderRadius: BORDER_RADIUS.sm,
        },
        buttonText: {
            fontSize: RESPONSIVE_FONT_SIZE.md[breakpoint],
            fontFamily: FONT_FAMILY.regular,
            fontWeight: FONT_WEIGHT.medium,
        },
        textContainer: {
            marginVertical: adjustSpacing(SPACING.sm),
        },
        textStyle: {
            fontSize: RESPONSIVE_FONT_SIZE.lg[breakpoint],
            fontFamily: FONT_FAMILY.regular,
            fontWeight: FONT_WEIGHT.medium,
        },
        errorText: {
            fontSize: RESPONSIVE_FONT_SIZE.sm[breakpoint],
            fontFamily: FONT_FAMILY.regular,
            color: 'red',
            marginTop: adjustSpacing(isAndroid ? 2 : SPACING.xs),
            ...(isAndroid && {
                lineHeight: LINE_HEIGHT,
            }),
        },
        input: {
            height: isAndroid ? INPUT_HEIGHT * 0.9 : INPUT_HEIGHT,
            paddingHorizontal: adjustSpacing(SPACING.sm),
            fontSize: RESPONSIVE_FONT_SIZE.md[breakpoint],
            fontFamily: FONT_FAMILY.regular,
            borderRadius: BORDER_RADIUS.sm,
            borderWidth: 1,
        },
    });
};

// Core function to generate responsive styles
export const getResponsiveStyles = (width: number, height: number) => {
    const breakpoint = getBreakpoint(width);
    // const orientation: Orientation = isLandscape(width, height) ? CONST_ORIENTATION.landscape : CONST_ORIENTATION.portrait;

    return StyleSheet.create({
        navigatorHeader: {
            height: RESPONSIVE_HEADER_HEIGHT[breakpoint],
        },
        container: {
            fontSize: RESPONSIVE_FONT_SIZE.lg[breakpoint],
        },
        card: {
            ...SHADOWS.medium,
            borderRadius: BORDER_RADIUS.sm,
            padding: adjustSpacing(SPACING.md),
            marginVertical: adjustSpacing(SPACING.sm),
        },
        text: {
            fontSize: RESPONSIVE_FONT_SIZE.lg[breakpoint],
            fontFamily: FONT_FAMILY.regular,
        },
        button: {
            ...BASE_STYLE.centerContent,
            height: isAndroid ? BUTTON_HEIGHT * 0.9 : BUTTON_HEIGHT,
            paddingHorizontal: adjustSpacing(SPACING.md),
            borderRadius: BORDER_RADIUS.sm,
        },
        buttonText: {
            fontSize: RESPONSIVE_FONT_SIZE.md[breakpoint],
            fontFamily: FONT_FAMILY.bold,
        },
    });
};

// Hook for dynamic responsive styles
export const useResponsiveStyles = () => {
    const { width, height } = useWindowDimensions();
    return useMemo(() => getResponsiveStyles(width, height), [width, height]);
};
import { useState, useCallback, useEffect } from 'react';
import { Platform } from 'react-native';
import Geolocation from 'react-native-geolocation-service';
import { Alert } from 'react-native';
import { PointLocation } from '@/components/Map/MapView';
import Config from 'react-native-config';

// Google Maps API Key - Use from environment variables
const API_KEY = Config.GOOGLE_MAPS_API_KEY;

// Direction result type
export interface RouteInfo {
    distance: number; // meters
    duration: number; // seconds
    coordinates: { latitude: number; longitude: number }[];
    startPoint: PointLocation;
    endPoint: PointLocation;
}

interface UseMapDirectionsProps {
    points: PointLocation[];
    isMultiRoute?: boolean;
    initialSelectedPointIndex?: number;
}

/**
 * Hook for handling routing and directions between points on the map
 */
const useMapDirections = ({
    points,
    isMultiRoute = false,
    initialSelectedPointIndex = 0,
}: UseMapDirectionsProps) => {
    // States
    const [userLocation, setUserLocation] = useState<{ latitude: number; longitude: number } | null>(null);
    const [selectedPointIndex, setSelectedPointIndex] = useState<number>(initialSelectedPointIndex);
    const [routes, setRoutes] = useState<RouteInfo[]>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);

    // Get current user location
    const getCurrentLocation = useCallback(() => {
        if (Platform.OS === 'ios') {
            Geolocation.requestAuthorization('whenInUse');
        }

        setLoading(true);
        Geolocation.getCurrentPosition(
            (position: any) => {
                const { latitude, longitude } = position.coords;
                setUserLocation({ latitude, longitude });
                setLoading(false);
            },
            (err: any) => {
                setError('Unable to get current location');
                setLoading(false);
                console.error('Location error:', err);
            },
            { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 }
        );
    }, []);

    // Get directions between two points from Google Maps API
    const getDirections = useCallback(async (
        startPoint: { latitude: number; longitude: number },
        endPoint: { latitude: number; longitude: number }
    ) => {
        try {
            const apiUrl = `https://maps.googleapis.com/maps/api/directions/json?origin=${startPoint.latitude
                },${startPoint.longitude}&destination=${endPoint.latitude},${endPoint.longitude
                }&mode=driving&key=${API_KEY}`;

            const response = await fetch(apiUrl);
            const result = await response.json();

            if (result.status !== 'OK') {
                throw new Error(result.error_message || 'Error getting directions');
            }

            const route = result.routes[0];
            if (!route) {
                throw new Error('No route found');
            }

            const leg = route.legs[0];
            const distance = leg.distance.value; // meters
            const duration = leg.duration.value; // seconds

            // Decode polyline
            const points = route.overview_polyline.points;
            const coordinates = decodePolyline(points).map(point => ({
                latitude: point[0],
                longitude: point[1],
            }));

            return {
                distance,
                duration,
                coordinates,
            };
        } catch (err) {
            console.error('Error fetching directions:', err);
            throw err;
        }
    }, [API_KEY]);

    // Decode polyline from Google Maps API
    const decodePolyline = (encoded: string) => {
        const poly = [];
        let index = 0, lat = 0, lng = 0;

        while (index < encoded.length) {
            let b, shift = 0, result = 0;

            do {
                b = encoded.charCodeAt(index++) - 63;
                result |= (b & 0x1f) << shift;
                shift += 5;
            } while (b >= 0x20);

            const dlat = ((result & 1) ? ~(result >> 1) : (result >> 1));
            lat += dlat;

            shift = 0;
            result = 0;

            do {
                b = encoded.charCodeAt(index++) - 63;
                result |= (b & 0x1f) << shift;
                shift += 5;
            } while (b >= 0x20);

            const dlng = ((result & 1) ? ~(result >> 1) : (result >> 1));
            lng += dlng;

            poly.push([lat / 1e5, lng / 1e5]);
        }

        return poly;
    };

    // Update route for selected point
    const updateRoute = useCallback(async () => {
        if (points.length === 0) return;

        const selectedPoint = points[selectedPointIndex];
        if (!selectedPoint) return;

        setLoading(true);
        setError(null);

        try {
            let startPoint;

            // If it's the first point, get directions from current location to that point
            if (selectedPointIndex === 0 || !isMultiRoute) {
                // Check if user location is available
                if (!userLocation) {
                    // If not, try to get current location
                    getCurrentLocation();
                    setLoading(false);
                    return; // Process will continue after getting location
                }
                startPoint = userLocation;
            } else {
                // If not the first point, get directions from previous point
                startPoint = {
                    latitude: points[selectedPointIndex - 1].latitude,
                    longitude: points[selectedPointIndex - 1].longitude,
                };
            }

            const endPoint = {
                latitude: selectedPoint.latitude,
                longitude: selectedPoint.longitude,
            };

            const directionResult = await getDirections(startPoint, endPoint);

            // Update routes
            const newRoute: RouteInfo = {
                ...directionResult,
                startPoint: (selectedPointIndex === 0 || !isMultiRoute)
                    ? {
                        id: 'user-location',
                        latitude: userLocation!.latitude,
                        longitude: userLocation!.longitude,
                        title: 'Your Location'
                    }
                    : points[selectedPointIndex - 1],
                endPoint: selectedPoint,
            };

            setRoutes(prev => {
                const updated = [...prev];
                updated[selectedPointIndex] = newRoute;
                return updated;
            });

            setLoading(false);
        } catch (err) {
            setError('Error getting route');
            setLoading(false);
            console.error('Error updating route:', err);
        }
    }, [points, selectedPointIndex, userLocation, isMultiRoute, getCurrentLocation, getDirections]);

    // Select a point
    const selectPoint = useCallback((index: number) => {
        if (index >= 0 && index < points.length) {
            setSelectedPointIndex(index);
        }
    }, [points]);

    // Get current location when mounted
    useEffect(() => {
        getCurrentLocation();
    }, [getCurrentLocation]);

    // Update route when selected point changes
    useEffect(() => {
        if (userLocation) {
            updateRoute();
        }
    }, [selectedPointIndex, userLocation, updateRoute]);

    // Handle when points list changes
    useEffect(() => {
        // Reset routes if the number of points changes
        setRoutes([]);

        // Reset selected point if it exceeds the number of points
        if (selectedPointIndex >= points.length && points.length > 0) {
            setSelectedPointIndex(0);
        }

        // Update route if user location is available and there are points
        if (userLocation && points.length > 0) {
            updateRoute();
        }
    }, [points, userLocation, updateRoute]);

    return {
        userLocation,
        getCurrentLocation,
        selectedPointIndex,
        selectPoint,
        routes,
        currentRoute: routes[selectedPointIndex],
        updateRoute,
        loading,
        error,
    };
};

export default useMapDirections; 
import React, { useState, useEffect } from 'react';
import { View, StyleSheet, TouchableOpacity, Platform } from 'react-native';
import { useTheme } from '@react-navigation/native';
import CText from '@/components/CText';
import PermissionButton from '@/components/PermissionButton';
import { PermissionType } from '@/hooks/usePermission';
import { FONT_WEIGHT } from '@/constants/UI/themes';

interface PermissionsManagerProps {
    permissions?: PermissionType[];
    title?: string;
    style?: any;
    onPermissionChange?: (permissionType: PermissionType, isGranted: boolean) => void;
}

// Ánh xạ quyền truy cập phù hợp với phiên bản Android
const mapToAppropriatePermission = (type: PermissionType): PermissionType => {
    // Đối với Android 13+ (API level 33), chuyển đổi quyền storage sang media
    if (Platform.OS === 'android' && Number(Platform.Version) >= 33) {
        switch (type) {
            case 'storage':
            case 'photoLibrary':
                return 'mediaImages';
            case 'writeStorage':
            case 'storageAndWrite':
                return 'mediaImages'; // Android 13+ không có WRITE_EXTERNAL_STORAGE
            default:
                return type;
        }
    }

    return type;
};

/**
 * Component quản lý và hiển thị danh sách các quyền
 */
const PermissionsManager: React.FC<PermissionsManagerProps> = ({
    permissions = ['camera', 'photoLibrary', 'microphone', 'location', 'storage'],
    title = 'Quản lý quyền truy cập',
    style,
    onPermissionChange,
}) => {
    const { colors } = useTheme() as any;
    const [permissionStates, setPermissionStates] = useState<Record<string, boolean>>({});

    // Ánh xạ quyền phù hợp với phiên bản Android
    const mappedPermissions = permissions.map(mapToAppropriatePermission);

    // Cập nhật trạng thái quyền khi có thay đổi
    const handlePermissionChange = (type: PermissionType, isGranted: boolean) => {
        setPermissionStates(prev => ({
            ...prev,
            [type]: isGranted
        }));

        if (onPermissionChange) {
            onPermissionChange(type, isGranted);
        }
    };

    // Lấy nhãn hiển thị cho loại quyền
    const getPermissionLabel = (type: PermissionType): string => {
        switch (type) {
            case 'camera':
                return 'Quyền camera';
            case 'photoLibrary':
                return 'Quyền thư viện ảnh';
            case 'microphone':
                return 'Quyền microphone';
            case 'location':
                return 'Quyền vị trí';
            case 'storage':
                return 'Quyền lưu trữ';
            case 'writeStorage':
                return 'Quyền ghi bộ nhớ';
            case 'mediaImages':
                return 'Quyền truy cập hình ảnh';
            case 'mediaVideo':
                return 'Quyền truy cập video';
            case 'mediaAudio':
                return 'Quyền truy cập âm thanh';
            default:
                return 'Quyền truy cập';
        }
    };

    return (
        <View style={[styles.container, style]}>
            {title && (
                <CText style={[styles.title, { color: colors.text }]}>
                    {title}
                </CText>
            )}

            <View style={styles.permissionsContainer}>
                {mappedPermissions.map((permission, index) => (
                    <PermissionButton
                        key={`${permission}-${index}`}
                        permissionType={permission}
                        label={getPermissionLabel(permission)}
                        onStatusChange={(isGranted) => handlePermissionChange(permission, isGranted)}
                    />
                ))}
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        padding: 16,
    },
    title: {
        fontSize: 18,
        fontWeight: FONT_WEIGHT.semiBold,
        marginBottom: 16,
    },
    permissionsContainer: {
        marginBottom: 16,
    },
    refreshButton: {
        padding: 12,
        borderRadius: 6,
        borderWidth: 1,
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: 8,
    },
    refreshButtonText: {
        fontSize: 14,
        fontWeight: FONT_WEIGHT.medium,
    },
});

export default PermissionsManager; 
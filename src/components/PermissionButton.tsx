import React from 'react';
import { TouchableOpacity, View, StyleSheet } from 'react-native';
import { useTheme } from '@react-navigation/native';
import CText from '@/components/CText';
import { RESULTS } from 'react-native-permissions';
import { Platform } from 'react-native';
import { PermissionType, usePermission } from '@/hooks/usePermission';

interface PermissionButtonProps {
    permissionType: PermissionType;
    label?: string;
    style?: any;
    buttonStyle?: any;
    textStyle?: any;
    onStatusChange?: (isGranted: boolean) => void;
}

/**
 * Component hiển thị nút yêu cầu quyền truy cập
 */
const PermissionButton: React.FC<PermissionButtonProps> = ({
    permissionType,
    label,
    style,
    buttonStyle,
    textStyle,
    onStatusChange,
}) => {
    const { colors } = useTheme() as any;
    const {
        status,
        isGranted,
        request,
        requestMediaPermissions,
        requestBothStoragePermissions,
        openSettings,
        getStatusText,
    } = usePermission(permissionType);

    // <PERSON>ác định nhãn mặc định dựa trên loại quyền
    const getDefaultLabel = () => {
        switch (permissionType) {
            case 'camera':
                return 'Quyền camera';
            case 'photoLibrary':
                return 'Quyền thư viện ảnh';
            case 'microphone':
                return 'Quyền microphone';
            case 'location':
                return 'Quyền vị trí';
            case 'storage':
                return 'Quyền lưu trữ';
            case 'writeStorage':
                return 'Quyền ghi bộ nhớ';
            case 'mediaImages':
                return 'Quyền truy cập hình ảnh';
            case 'mediaVideo':
                return 'Quyền truy cập video';
            case 'mediaAudio':
                return 'Quyền truy cập âm thanh';
            default:
                return 'Yêu cầu quyền';
        }
    };

    // Xử lý khi nhấn nút yêu cầu quyền
    const handleRequestPermission = async () => {
        let granted = false;

        // Xử lý khác nhau tùy thuộc vào loại quyền và nền tảng
        if (Platform.OS === 'android' && Number(Platform.Version) >= 33) {
            // Android 13+ cần xử lý quyền media cụ thể
            if (['storage', 'photoLibrary', 'writeStorage', 'storageAndWrite'].includes(permissionType)) {
                granted = await requestMediaPermissions?.() || false;
            } else {
                granted = await request();
            }
        } else if (permissionType === 'storageAndWrite' || permissionType === 'writeStorage') {
            // Android <13 cần xử lý cả quyền đọc và ghi
            granted = await requestBothStoragePermissions?.() || false;
        } else {
            // Các trường hợp còn lại
            granted = await request();
        }

        if (onStatusChange) {
            onStatusChange(granted);
        }
    };

    // Xử lý khi nhấn nút mở cài đặt
    const handleOpenSettings = async () => {
        await openSettings();
    };

    // Kiểm tra xem quyền có bị chặn hoặc từ chối không
    const isBlockedOrDenied = status === RESULTS.BLOCKED ||
        (Platform.OS === 'ios' && status === RESULTS.DENIED);

    return (
        <View style={[styles.container, style]}>
            <View style={styles.statusContainer}>
                <CText style={[styles.statusText, { color: colors.text }]}>
                    {label || getDefaultLabel()}: {getStatusText()}
                </CText>
            </View>

            <View style={styles.buttonsContainer}>
                {/* Nút yêu cầu quyền */}
                <TouchableOpacity
                    style={[
                        styles.button,
                        { backgroundColor: colors.primary },
                        buttonStyle
                    ]}
                    onPress={handleRequestPermission}
                >
                    <CText style={[styles.buttonText, textStyle]}>
                        Yêu cầu quyền
                    </CText>
                </TouchableOpacity>

                {/* Hiển thị nút mở cài đặt nếu quyền bị chặn hoặc từ chối */}
                {isBlockedOrDenied && (
                    <TouchableOpacity
                        style={[
                            styles.button,
                            { backgroundColor: colors.notification, marginLeft: 8 },
                            buttonStyle
                        ]}
                        onPress={handleOpenSettings}
                    >
                        <CText style={[styles.buttonText, textStyle]}>
                            Mở cài đặt
                        </CText>
                    </TouchableOpacity>
                )}
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        marginBottom: 12,
    },
    statusContainer: {
        marginBottom: 8,
    },
    statusText: {
        fontSize: 14,
    },
    buttonsContainer: {
        flexDirection: 'row',
    },
    button: {
        paddingVertical: 8,
        paddingHorizontal: 16,
        borderRadius: 6,
        justifyContent: 'center',
        alignItems: 'center',
    },
    buttonText: {
        color: 'white',
        fontSize: 14,
        fontWeight: '500',
    },
});

export default PermissionButton; 
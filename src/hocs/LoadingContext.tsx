import React, { createContext, useContext, useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { showLoading, hideLoading } from '@/redux/slices/loadingSlice';
import Loading from '@/components/Loading';

interface LoadingContextType {
    show: () => void;
    hide: () => void;
    withLoading: (callback: () => Promise<any>) => Promise<void>;
}

const LoadingContext = createContext<LoadingContextType | undefined>(undefined);

export const LoadingProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const dispatch = useDispatch();

    const show = useCallback(() => {
        dispatch(showLoading());
    }, [dispatch]);

    const hide = useCallback(() => {
        dispatch(hideLoading());
    }, [dispatch]);

    const withLoading = useCallback(async (callback: () => Promise<any>) => {
        try {
            show();
            await callback();
        } finally {
            hide();
        }
    }, [show, hide]);

    return (
        <LoadingContext.Provider value={{ show, hide, withLoading }}>
            {children}
            <Loading />
        </LoadingContext.Provider>
    );
};

export const useLoading = () => {
    const context = useContext(LoadingContext);
    if (!context) {
        throw new Error('useLoading must be used within a LoadingProvider');
    }
    return context;
};
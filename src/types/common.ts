import { FontSizeKeys, SpacingKeys, BorderRadiusKeys } from "./theme";
import { HEADER_HEIGHT, ICON_SIZE } from "../constants/UI/themes";

export type StatusBar = 'dark-content' | 'light-content';
export type ToastStatus = 'error' | 'success' | 'warning';
export type TaskStatus = 'scheduled' | 'complete' | 'pending';
export type BatchStatus = 'confirmed' | 'done' | 'partial';
export type PaymentStatus = 'posted' | 'pending' | 'failed';

export interface ToastContextType {
    showToast: (message: string, type?: ToastStatus) => void;
}

export type DeviceType = 'mobile' | 'tablet' | 'desktop';
export type Orientation = 'portrait' | 'landscape';
export type IconSizeKeys = keyof typeof ICON_SIZE;
export type HeaderHeightKeys = keyof typeof HEADER_HEIGHT;

// Type map for responsive categories
export type ResponsiveCategoryMap = {
    fontSize: FontSizeKeys;
    spacing: SpacingKeys;
    borderRadius: BorderRadiusKeys;
    iconSize: IconSizeKeys;
    headerHeight: HeaderHeightKeys;
}; 
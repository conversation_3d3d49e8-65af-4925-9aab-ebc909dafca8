import { User } from '@/types';

export const usersMockData: User[] = [
    {
        id: "user001",
        fullName: "<PERSON><PERSON><PERSON>",
        email: "<EMAIL>",
        password: "Password@123",
        role: "technician",
        phoneNumber: "0987654321",
        profileImageUrl: "https://randomuser.me/api/portraits/men/1.jpg"
    },
    {
        id: "user002",
        fullName: "Tran Thi Manager",
        email: "<EMAIL>",
        password: "Password@123",
        role: "manager",
        phoneNumber: "0912345678",
        profileImageUrl: "https://randomuser.me/api/portraits/women/1.jpg"
    },
    {
        id: "user003",
        fullName: "<PERSON> Admin",
        email: "<EMAIL>",
        password: "Password@123",
        role: "admin",
        phoneNumber: "0909123456",
        profileImageUrl: "https://randomuser.me/api/portraits/men/2.jpg"
    }
];

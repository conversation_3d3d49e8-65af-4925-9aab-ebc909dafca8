import React from 'react';
import { View, StyleSheet, ViewStyle, StyleProp, DimensionValue } from 'react-native';
import { useWindowDimensions } from 'react-native';
import { SHADOWS, SPACING, BORDER_RADIUS } from '../constants/UI/themes';
import { getBreakpoint, isLandscape } from '../constants/UI/responsive';
import { DeviceType } from '../types';
import { useMaxWidth } from '@/utils/layout';

interface CCardProps {
    children: React.ReactNode;
    style?: StyleProp<ViewStyle>;
}

const CCard: React.FC<CCardProps> = ({ children, style }) => {
    const { width, height } = useWindowDimensions();
    const breakpoint: DeviceType = getBreakpoint(width);
    const landscape = isLandscape(width, height);
    const reWidth = useMaxWidth()
    const maxWidth = breakpoint === 'mobile' ? reWidth * 0.9 : (reWidth / 2) * 0.8

    const getCardStyle = () => {
        const baseStyle: ViewStyle = {
            // backgroundColor: 'red',
            borderRadius: BORDER_RADIUS.md,
            padding: SPACING.md,
            ...SHADOWS.medium,
            maxWidth: maxWidth,

        };

        if (breakpoint === 'tablet') {
            return {
                ...baseStyle,
                width: landscape ? '100%' as DimensionValue : '70%' as DimensionValue,
                padding: SPACING.lg,
                borderWidth: 2
            };
        }

        // Mobile style
        return {
            ...baseStyle,
            width: landscape ? '45%' as DimensionValue : '90%' as DimensionValue,
        };
    };

    return (
        <View style={[styles.container, getCardStyle(), style]}>
            {children}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        alignSelf: 'center',
        marginVertical: SPACING.md,
    },
});

export default CCard; 
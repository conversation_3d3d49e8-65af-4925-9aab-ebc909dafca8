// Toast helper utility to handle toast messages without direct dependency

interface ToastOptions {
    type: 'success' | 'error' | 'info';
    text1?: string;
    text2?: string;
    position?: 'top' | 'bottom';
    visibilityTime?: number;
    autoHide?: boolean;
    topOffset?: number;
    bottomOffset?: number;
}

class ToastHelper {
    static show(options: ToastOptions): void {
        // In a real implementation, this would call the actual Toast library
        // console.log('Toast message:', options);

        // If react-native-toast-message is installed, this would call:
        // Toast.show(options);

        // For now, we'll just log to console
        console.log(`[${options.type.toUpperCase()}] ${options.text1}: ${options.text2}`);
    }
}

export default ToastHelper; 
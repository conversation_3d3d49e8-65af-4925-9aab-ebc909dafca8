import React from 'react';
import { useForm, UseFormReturn } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as Yup from 'yup';

interface WithFormProps<T> extends UseFormReturn<any> {
    onSubmit: (data: T) => void;
}

interface FormProps<T> {
    defaultValues: any;
    schema: Yup.ObjectSchema<any>;
    onSubmit: (data: T) => void;
}

const withForm = <T extends object>(
    WrappedComponent: React.ComponentType<WithFormProps<T>>
) => {
    return (props: FormProps<T>) => {
        const { defaultValues, schema, onSubmit } = props;

        const methods: UseFormReturn<any> = useForm<T>({
            defaultValues,
            resolver: yupResolver(schema),
        });

        return <WrappedComponent {...props} {...methods} onSubmit={onSubmit} />;
    };
};

export default withForm;
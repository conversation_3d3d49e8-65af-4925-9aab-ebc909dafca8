import React, { useEffect, useState } from 'react';
import { View, Text, Button, Platform, Alert, StyleSheet, Linking } from 'react-native';
import { check, request, PERMISSIONS, RESULTS } from 'react-native-permissions';

function LocationPermission() {
    const [permissionStatus, setPermissionStatus] = useState('Chưa kiểm tra');

    // Hàm kiểm tra trạng thái quyền vị trí
    const checkLocationPermission = () => {
        const permission =
            Platform.OS === 'ios'
                ? PERMISSIONS.IOS.LOCATION_WHEN_IN_USE // Quyền vị trí khi ứng dụng đang sử dụng (iOS)
                : PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION; // Quyền vị trí chính xác (Android)

        check(permission).then((status) => {
            console.log('Location permission status :>> ', status);
            setPermissionStatus(status);

            if (status === 'denied') {
                Alert.alert(
                    'Quyền truy cập vị trí bị từ chối',
                    'Ứng dụng cần quyền truy cập vị trí để hỗ trợ dịch vụ tại chỗ. Bạn có muốn yêu cầu lại quyền này không?',
                    [
                        { text: 'Hủy', style: 'cancel' },
                        { text: 'Yêu cầu lại', onPress: requestLocationPermission },
                    ]
                );
            } else if (status === 'blocked') {
                Alert.alert(
                    'Quyền truy cập vị trí bị chặn',
                    'Quyền truy cập vị trí đã bị chặn. Vui lòng vào cài đặt để cấp quyền.',
                    [
                        { text: 'Hủy', style: 'cancel' },
                        { text: 'Mở cài đặt', onPress: () => Linking.openSettings() },
                    ]
                );
            }
        });
    };

    // Hàm yêu cầu quyền vị trí
    const requestLocationPermission = async () => {
        try {
            const permission =
                Platform.OS === 'ios'
                    ? PERMISSIONS.IOS.LOCATION_WHEN_IN_USE
                    : PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION;

            const result = await request(permission);

            if (result === RESULTS.GRANTED) {
                setPermissionStatus('granted');
                Alert.alert('Thành công', 'Quyền truy cập vị trí đã được cấp.');
            } else if (result === RESULTS.DENIED) {
                setPermissionStatus('denied');
                Alert.alert('Quyền bị từ chối', 'Bạn đã từ chối quyền truy cập vị trí. Vui lòng cấp quyền trong cài đặt.');
            } else if (result === RESULTS.BLOCKED) {
                setPermissionStatus('blocked');
                Alert.alert(
                    'Quyền bị chặn',
                    'Quyền truy cập vị trí đã bị chặn. Vui lòng vào cài đặt để cấp quyền.',
                    [
                        { text: 'Hủy', style: 'cancel' },
                        { text: 'Mở cài đặt', onPress: () => Linking.openSettings() },
                    ]
                );
            } else {
                setPermissionStatus('unavailable');
                Alert.alert('Quyền không khả dụng', 'Quyền này không khả dụng trên thiết bị của bạn.');
            }
        } catch (error: any) {
            console.log(error);
            setPermissionStatus('error');
            Alert.alert('Lỗi', 'Đã có lỗi xảy ra khi yêu cầu quyền vị trí: ' + error.message);
        }
    };

    // Kiểm tra quyền khi component được mount
    useEffect(() => {
        checkLocationPermission();
    }, []);

    return (
        <View style={styles.container}>
            <Text style={styles.title}>Yêu cầu quyền truy cập vị trí</Text>
            <Text style={styles.status}>Trạng thái: {permissionStatus}</Text>
            {permissionStatus === 'denied' && (
                <Button title="Yêu cầu lại quyền vị trí" onPress={requestLocationPermission} />
            )}
            {permissionStatus === 'blocked' && (
                <Button title="Mở cài đặt để cấp quyền" onPress={() => Linking.openSettings()} />
            )}
            {permissionStatus === 'granted' && (
                <Text style={styles.success}>Bạn đã cấp quyền truy cập vị trí!</Text>
            )}
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: 20,
        backgroundColor: '#f5f5f5',
        justifyContent: 'center',
        alignItems: 'center',
    },
    title: {
        fontSize: 20,
        fontWeight: 'bold',
        marginBottom: 20,
        color: '#2d4150',
    },
    status: {
        fontSize: 16,
        marginBottom: 20,
        color: '#2d4150',
    },
    success: {
        fontSize: 16,
        color: 'green',
        marginTop: 10,
    },
});

export default LocationPermission;
<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
        <item name="android:windowBackground">@drawable/background_splash</item>
        <item name="android:statusBarColor">@color/primary_dark</item>
        <item name="android:windowDisablePreview">true</item>
    </style>

    <style name="SplashTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowBackground">@drawable/background_splash</item>
        <item name="android:statusBarColor">@color/primary_dark</item>
        <item name="android:windowDisablePreview">true</item>
    </style>

</resources>

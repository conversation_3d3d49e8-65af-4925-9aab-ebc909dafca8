import React, { useMemo } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { format, startOfWeek, addDays } from 'date-fns';
import { SPACING, COLORS } from '@/constants/UI/themes';
import { useTranslation } from 'react-i18next';

interface WeekDaysProps {
    selectedDate: Date;
    onDayPress: (date: Date) => void;
}

export const WeekDays: React.FC<WeekDaysProps> = ({ selectedDate, onDayPress }) => {
    const { t } = useTranslation();

    // Tính toán các ngày trong tuần dựa trên ngày được chọn
    const weekDays = useMemo(() => {
        const start = startOfWeek(selectedDate, { weekStartsOn: 1 }); // Bắt đầu từ thứ 2
        return Array.from({ length: 7 }, (_, i) => addDays(start, i));
    }, [selectedDate]);

    // Hàm trả về key tương ứng với ngày trong tuần
    const getDayKey = (day: number): string => {
        const keys = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];
        return keys[day - 1] || 'mon'; // Trả về 'mon' nếu không tìm thấy
    };

    // Hàm lấy tên viết tắt của thứ theo ngôn ngữ hiện tại
    const getWeekdayName = (date: Date): string => {
        const day = date.getDay(); // 0 = Chủ nhật, 1 = Thứ hai, ...
        // Điều chỉnh để 1 = Thứ hai, ..., 7 = Chủ nhật
        const adjustedDay = day === 0 ? 7 : day;
        const key = getDayKey(adjustedDay);
        return t(`weekDays.${key}`);
    };

    return (
        <View style={styles.container}>
            {weekDays.map((date, index) => {
                const isSelected = format(date, 'yyyy-MM-dd') === format(selectedDate, 'yyyy-MM-dd');
                return (
                    <TouchableOpacity
                        key={index}
                        style={[
                            styles.dayContainer,
                            isSelected && styles.selectedDayContainer
                        ]}
                        onPress={() => onDayPress(date)}
                    >
                        <Text style={[
                            styles.dayName,
                            isSelected && styles.selectedText
                        ]}>
                            {getWeekdayName(date)}
                        </Text>

                        <View style={[
                            styles.separator,
                            isSelected && styles.selectedSeparator
                        ]} />

                        <Text style={[
                            styles.dayNumber,
                            isSelected && styles.selectedText
                        ]}>
                            {format(date, 'd')}
                        </Text>
                    </TouchableOpacity>
                );
            })}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        backgroundColor: COLORS.light.white,
        paddingHorizontal: SPACING.md,
        paddingVertical: SPACING.sm,
    },
    dayContainer: {
        width: 50,
        height: 70,
        borderRadius: 14.25,
        alignItems: 'center',
        justifyContent: 'space-between',
        backgroundColor: COLORS.light.white,
        // Shadow for iOS
        shadowColor: '#000000',
        shadowOffset: { width: 0, height: 2.85 },
        shadowOpacity: 0.25,
        shadowRadius: 2.85,
        // Shadow for Android
        elevation: 4,
        paddingVertical: 8,
    },
    selectedDayContainer: {
        backgroundColor: COLORS.light.primary,
    },
    dayName: {
        fontFamily: 'Lato',
        fontWeight: '400',
        fontSize: 18,
        lineHeight: 18, // 100% of font size
        textAlign: 'center',
        color: COLORS.light.text,
    },
    separator: {
        width: 29,
        borderWidth: 0.71,
        borderColor: COLORS.light.border,
        marginVertical: 7.12, // Using the gap value as marginVertical
    },
    selectedSeparator: {
        borderColor: COLORS.light.white,
    },
    dayNumber: {
        fontFamily: 'Lato',
        fontWeight: '400',
        fontSize: 18,
        lineHeight: 18, // 100% of font size
        textAlign: 'center',
        color: COLORS.light.text,
    },
    selectedText: {
        color: COLORS.light.white,
    },
}); 
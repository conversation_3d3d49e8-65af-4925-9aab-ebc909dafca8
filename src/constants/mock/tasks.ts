import { Task } from '@/interfaces/task';

export const mockTasks = {
    status: "success",
    total: 15,
    limit: 12,
    offset: 0,
    has_more: true,
    records: [
        {
            task_id: 101,
            task_code: "WH/IN/S00121/Installation",
            task_type_icon_url: "/web/image?model=product.product&id=101&field=image_128&token=icon123",
            status: "completed",
            scheduled_from: "2025-04-13T10:00:00Z",
            scheduled_to: "2025-04-13T12:00:00Z",
            customer: {
                name: "<PERSON><PERSON>",
                phone: "+84901234567",
                address: "15 Le Loi, HCMC"
            },
            products: ["Refrigerator", "Air Conditioner"]
        },
        {
            task_id: 102,
            task_code: "WH/OUT/S00123/Drop off",
            task_type_icon_url: "/web/image?model=product.product&id=999&field=image_128&token=icon123",
            status: "scheduled",
            scheduled_from: "2025-04-14T08:00:00Z",
            scheduled_to: "2025-04-14T09:00:00Z",
            customer: {
                name: "<PERSON>",
                phone: "+84901011223",
                address: "23 <PERSON><PERSON><PERSON>, HCMC"
            },
            products: ["Washing Machine", "TV OLED"]
        },
        {
            task_id: 103,
            task_code: "CW/RO/S00126/Basic service",
            task_type_icon_url: "/web/image?model=product.product&id=100&field=image_128&token=icon123",
            status: "scheduled",
            scheduled_from: "2025-04-15T13:30:00Z",
            scheduled_to: "2025-04-15T15:00:00Z",
            repair_product: "Washing Machine",
            customer: {
                name: "Mai Linh",
                phone: "+84938383838",
                address: "88 Tran Hung Dao, HCMC"
            }
        },
        {
            task_id: 104,
            task_code: "CW/IN/S00127/Premium Setup",
            task_type_icon_url: "/web/image?model=product.product&id=102&field=image_128&token=icon123",
            status: "in_progress",
            scheduled_from: "2025-04-15T09:00:00Z",
            scheduled_to: "2025-04-15T11:30:00Z",
            customer: {
                name: "Nguyen Thi Lan",
                phone: "+84912345678",
                address: "45 Le Thanh Ton, HCMC"
            },
            products: ["Smart TV", "Home Theater System"]
        },
        {
            task_id: 105,
            task_code: "WH/OUT/S00128/Delivery",
            task_type_icon_url: "/web/image?model=product.product&id=103&field=image_128&token=icon123",
            status: "scheduled",
            scheduled_from: "2025-04-16T14:00:00Z",
            scheduled_to: "2025-04-16T16:00:00Z",
            customer: {
                name: "Pham Van Duc",
                phone: "+84977777777",
                address: "101 Vo Van Tan, HCMC"
            },
            products: ["Refrigerator"]
        },
        {
            task_id: 106,
            task_code: "CW/RE/S00129/Emergency Repair",
            task_type_icon_url: "/web/image?model=product.product&id=104&field=image_128&token=icon123",
            status: "scheduled",
            scheduled_from: "2025-04-16T10:00:00Z",
            scheduled_to: "2025-04-16T12:00:00Z",
            repair_product: "Air Conditioner",
            customer: {
                name: "Tran Kim Hoa",
                phone: "+84909090909",
                address: "72 Nguyen Trai, HCMC"
            }
        },
        {
            task_id: 107,
            task_code: "WH/IN/S00130/Standard Setup",
            task_type_icon_url: "/web/image?model=product.product&id=105&field=image_128&token=icon123",
            status: "completed",
            scheduled_from: "2025-04-17T08:30:00Z",
            scheduled_to: "2025-04-17T10:30:00Z",
            customer: {
                name: "Le Thi Hanh",
                phone: "+84988888888",
                address: "55 Dien Bien Phu, HCMC"
            },
            products: ["Microwave", "Blender"]
        },
        {
            task_id: 108,
            task_code: "WH/OUT/S00131/Check Product",
            task_type_icon_url: "/web/image?model=product.product&id=106&field=image_128&token=icon123",
            status: "scheduled",
            scheduled_from: "2025-04-17T13:00:00Z",
            scheduled_to: "2025-04-17T14:30:00Z",
            customer: {
                name: "Nguyen Van Binh",
                phone: "+84966666666",
                address: "33 Cach Mang Thang 8, HCMC"
            },
            products: ["Dishwasher"]
        },
        {
            task_id: 109,
            task_code: "CW/RE/S00132/Maintenance",
            task_type_icon_url: "/web/image?model=product.product&id=107&field=image_128&token=icon123",
            status: "in_progress",
            scheduled_from: "2025-04-18T09:00:00Z",
            scheduled_to: "2025-04-18T11:00:00Z",
            repair_product: "Refrigerator",
            customer: {
                name: "Hoang Thi Mai",
                phone: "+84944444444",
                address: "12 Ly Tu Trong, HCMC"
            }
        },
        {
            task_id: 110,
            task_code: "WH/IN/S00133/Advanced Setup",
            task_type_icon_url: "/web/image?model=product.product&id=108&field=image_128&token=icon123",
            status: "scheduled",
            scheduled_from: "2025-04-18T15:00:00Z",
            scheduled_to: "2025-04-18T17:30:00Z",
            customer: {
                name: "Tran Van Cuong",
                phone: "+84922222222",
                address: "78 Nam Ky Khoi Nghia, HCMC"
            },
            products: ["Smart Home System", "Security Cameras"]
        },
        {
            task_id: 111,
            task_code: "CW/RO/S00134/Consultation",
            task_type_icon_url: "/web/image?model=product.product&id=109&field=image_128&token=icon123",
            status: "scheduled",
            scheduled_from: "2025-04-19T10:30:00Z",
            scheduled_to: "2025-04-19T11:30:00Z",
            customer: {
                name: "Vo Thi Huong",
                phone: "+84933333333",
                address: "44 Truong Dinh, HCMC"
            },
            products: ["Home Appliance Package"]
        },
        {
            task_id: 112,
            task_code: "WH/OUT/S00135/Return",
            task_type_icon_url: "/web/image?model=product.product&id=110&field=image_128&token=icon123",
            status: "completed",
            scheduled_from: "2025-04-19T14:00:00Z",
            scheduled_to: "2025-04-19T15:00:00Z",
            customer: {
                name: "Le Van Thanh",
                phone: "+84955555555",
                address: "90 Nguyen Du, HCMC"
            },
            products: ["Vacuum Cleaner"]
        },
        {
            task_id: 113,
            task_code: "CW/RE/S00136/Critical Fix",
            task_type_icon_url: "/web/image?model=product.product&id=111&field=image_128&token=icon123",
            status: "scheduled",
            scheduled_from: "2025-04-20T08:00:00Z",
            scheduled_to: "2025-04-20T10:00:00Z",
            repair_product: "Freezer",
            customer: {
                name: "Nguyen Thi Hong",
                phone: "+84911111111",
                address: "67 Le Van Sy, HCMC"
            }
        },
        {
            task_id: 114,
            task_code: "WH/IN/S00137/Installation",
            task_type_icon_url: "/web/image?model=product.product&id=112&field=image_128&token=icon123",
            status: "in_progress",
            scheduled_from: "2025-04-20T13:30:00Z",
            scheduled_to: "2025-04-20T16:00:00Z",
            customer: {
                name: "Tran Minh Tuan",
                phone: "+84999999999",
                address: "24 Ba Thang Hai, HCMC"
            },
            products: ["Water Purifier", "Heater"]
        },
        {
            task_id: 115,
            task_code: "CW/RO/S00138/Warranty Service",
            task_type_icon_url: "/web/image?model=product.product&id=113&field=image_128&token=icon123",
            status: "scheduled",
            scheduled_from: "2025-04-21T09:30:00Z",
            scheduled_to: "2025-04-21T11:00:00Z",
            repair_product: "Coffee Machine",
            customer: {
                name: "Le Thi Quynh",
                phone: "+84900000000",
                address: "35 Hai Ba Trung, HCMC"
            }
        }
    ]
}; 
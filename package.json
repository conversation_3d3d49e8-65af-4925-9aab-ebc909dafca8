{"name": "ARC", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "android:dev": "npx react-native run-android --mode debug --port 8081 --appId net.avb.arc.debug", "android:stg": "npx react-native run-android --mode staging --port 8082 --appId net.avb.arc.staging", "android:pro": "npx react-native run-android --mode release --port 8083 --appId net.avb.arc", "ios:dev": "npx react-native run-ios --mode Debug --simulator=\"iPhone 15\"", "ios:stg": "npx react-native run-ios --mode Staging --simulator=\"iPhone 15\"", "ios:pro": "npx react-native run-ios --mode Release --simulator=\"iPhone 15\"", "ios:device:dev": "npx react-native run-ios --mode Debug", "ios:device:stg": "npx react-native run-ios --mode Staging", "ios:device:pro": "npx react-native run-ios --mode Release", "ios:list-devices": "xcrun xctrace list devices"}, "dependencies": {"@gorhom/bottom-sheet": "^5.1.2", "@hookform/resolvers": "^5.0.1", "@kichiyaki/react-native-barcode-generator": "^0.6.7", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-camera-roll/camera-roll": "^7.10.0", "@react-navigation/drawer": "^7.3.2", "@react-navigation/elements": "^2.3.1", "@react-navigation/native": "^7.0.19", "@react-navigation/native-stack": "^7.3.3", "@reduxjs/toolkit": "^2.6.1", "axios": "^1.8.4", "babel-plugin-module-resolver": "^5.0.2", "date-fns": "^4.1.0", "i18next": "^25.1.2", "jwt-decode": "^4.0.0", "react": "19.0.0", "react-hook-form": "^7.55.0", "react-i18next": "^15.5.1", "react-native": "0.78.1", "react-native-calendars": "^1.1312.0", "react-native-config": "^1.5.5", "react-native-fs": "^2.20.0", "react-native-geolocation-service": "^5.3.1", "react-native-gesture-handler": "^2.24.0", "react-native-keychain": "^10.0.0", "react-native-linear-gradient": "^2.8.3", "react-native-maps": "^1.23.7", "react-native-permissions": "^5.3.0", "react-native-qrcode-svg": "^6.3.15", "react-native-reanimated": "^3.17.5", "react-native-safe-area-context": "^5.3.0", "react-native-screens": "^4.10.0", "react-native-svg": "^15.11.2", "react-native-vision-camera": "^4.6.4", "react-redux": "^9.2.0", "redux-logger": "^3.0.6", "yup": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.78.1", "@react-native/eslint-config": "0.78.1", "@react-native/metro-config": "0.78.1", "@react-native/typescript-config": "0.78.1", "@types/jest": "^29.5.13", "@types/react": "^19.0.0", "@types/react-test-renderer": "^19.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}
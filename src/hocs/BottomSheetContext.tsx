import React, { createContext, useContext, useRef, useState, ReactNode } from 'react';
import BottomSheet, { BottomSheetView, BottomSheetBackdropProps } from '@gorhom/bottom-sheet';
import { Text, StyleSheet, TouchableOpacity } from 'react-native';

interface OpenBottomSheetOptions {
    content: ReactNode;
    snapPoints?: (string | number)[];
}

interface BottomSheetContextType {
    openBottomSheet: (options: OpenBottomSheetOptions) => void;
    closeBottomSheet: () => void;
    isBottomSheetOpen: boolean;
}

const BottomSheetContext = createContext<BottomSheetContextType | undefined>(undefined);


const BottomSheetBackdrop = ({ onPress }: BottomSheetBackdropProps & { onPress: () => void }) => {
    const { isBottomSheetOpen } = useBottomSheet();
    if (!isBottomSheetOpen) {
        return null;
    }

    return (
        <TouchableOpacity
            style={styles.backdrop}
            activeOpacity={0.9}
            onPress={onPress}
        />
    );
};

export const BottomSheetProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const bottomSheetRef = useRef<BottomSheet>(null);
    const [isBottomSheetOpen, setIsBottomSheetOpen] = useState(false);
    const [sheetContent, setSheetContent] = useState<ReactNode>(null);
    const [snapPoints, setSnapPoints] = useState<(string | number)[]>(['55%', '70%']);

    const openBottomSheet = ({ content, snapPoints }: OpenBottomSheetOptions) => {
        console.log('openBottomSheet is called');
        setSheetContent(content);
        if (snapPoints) {
            setSnapPoints(snapPoints);
        }
        bottomSheetRef.current?.expand();
        setIsBottomSheetOpen(true);
    };

    const closeBottomSheet = () => {
        console.log('closeBottomSheet is called');
        bottomSheetRef.current?.close();
        setIsBottomSheetOpen(false);
    };

    return (
        <BottomSheetContext.Provider value={{ openBottomSheet, closeBottomSheet, isBottomSheetOpen }}>
            {children}
            <BottomSheet
                ref={bottomSheetRef}
                index={-1}
                snapPoints={snapPoints}
                enablePanDownToClose

                onChange={(index) => {
                    console.log('BottomSheet index:', index);
                    setIsBottomSheetOpen(index >= 0);
                }}
                backdropComponent={(props) => (
                    <BottomSheetBackdrop {...props} onPress={closeBottomSheet} />
                )}
            >
                <BottomSheetView style={styles.contentContainer}>
                    {sheetContent}
                </BottomSheetView>
            </BottomSheet>
        </BottomSheetContext.Provider>
    );
};

export const useBottomSheet = () => {
    const context = useContext(BottomSheetContext);
    if (!context) {
        throw new Error('useBottomSheet must be used within a BottomSheetProvider');
    }
    return context;
};

const styles = StyleSheet.create({
    contentContainer: {
        padding: 20,
        alignItems: 'center',
    },
    backdrop: {
        ...StyleSheet.absoluteFillObject,
        backgroundColor: 'rgba(244, 240, 240, 0.5)',
    },
});

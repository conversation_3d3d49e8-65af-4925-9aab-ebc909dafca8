import React from 'react';
import {
    Text,
    TextProps,
    TouchableOpacity,
    StyleSheet,
    GestureResponderEvent,
    ViewStyle,
    TextStyle,
} from 'react-native';
import { useTheme } from '@react-navigation/native';
import { FONT_FAMILY, FONT_SIZE, FONT_WEIGHT } from '@/constants/UI/themes';
import { getCommonStyles } from '@/constants/UI/responsiveStyles';

interface CButtonProps extends TextProps {
    children: React.ReactNode;
    weight?: keyof typeof FONT_WEIGHT;
    size?: keyof typeof FONT_SIZE;
    onPress?: (event: GestureResponderEvent) => void;
    style?: ViewStyle;
    textStyle?: TextStyle;
}

const CButton: React.FC<CButtonProps> = ({
    children,
    weight = 'semiBold',
    size = 'lg',
    onPress,
    style,
    textStyle,
    ...props
}) => {

    const { colors } = useTheme();
    const commonStyles = getCommonStyles();

    return (
        <TouchableOpacity
            onPress={onPress}
            activeOpacity={0.8}
            style={[
                commonStyles.button,
                {
                    backgroundColor: colors.primary,
                },
                style,
            ]}
        >
            <Text
                style={[
                    {
                        color: colors.text,
                        fontFamily: FONT_FAMILY["bold"],
                        fontSize: FONT_SIZE[size],
                    },
                    textStyle,
                ]}
                {...props}
            >
                {children}
            </Text>
        </TouchableOpacity>
    );
};

export default CButton;


import { configureStore, Middleware } from '@reduxjs/toolkit';
import logger from './middleware/logger';
import rootReducer from './reducers';

const timingMiddleware: Middleware = () => (next) => (action: any) => {
    const start = Date.now();
    const result = next(action);
    const duration = Date.now() - start;
    console.log(`Action ${action.type} took ${duration}ms`);
    return result;
};

const store = configureStore({
    reducer: rootReducer,
    middleware: (getDefaultMiddleware) =>
        getDefaultMiddleware().concat(logger, timingMiddleware),
    devTools: {
        name: 'ARC',
        trace: true,
    },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export default store;
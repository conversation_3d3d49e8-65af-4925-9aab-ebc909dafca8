import React, { useState } from 'react';
import { StyleSheet, View, ActivityIndicator, useWindowDimensions } from 'react-native';
import ResponsiveFlatList from './ResponsiveFlatList';
import { useTheme } from '@react-navigation/native';
import { SPACING, COLORS } from '../constants/UI/themes';
import { Task, TaskListData } from '@/interfaces/task';
import { getBreakpoint, scale } from '@/constants/UI/responsive';
import TaskItem from './TaskItem';

interface TaskListProps {
    data: TaskListData;
    onRefresh?: () => void;
    refreshing?: boolean;
    onLoadMore?: () => void;
}

export const TaskList = ({ data, onRefresh, refreshing = false, onLoadMore }: TaskListProps) => {
    const theme = useTheme();
    const [isLoadingMore, setIsLoadingMore] = useState(false);
    const { width } = useWindowDimensions();
    const handleLoadMore = () => {
        if (data.has_more && !isLoadingMore && onLoadMore) {
            setIsLoadingMore(true);
            onLoadMore();
            // Simulate network request with 2 second delay
            setTimeout(() => {
                setIsLoadingMore(false);
            }, 2000);
        }
    };

    return (
        <ResponsiveFlatList
            data={data.records}
            renderItem={({ item }: { item: Task }) => <TaskItem item={item} />}
            keyExtractor={(item: Task) => item.task_id.toString()}
            ItemSeparatorComponent={() => (
                <View style={[styles.separator, { backgroundColor: theme.colors.primary }]} />
            )}
            contentContainerStyle={styles.listContainer}
            numColumns={1}
            onRefresh={onRefresh}
            refreshing={refreshing}
            onEndReached={handleLoadMore}
            onEndReachedThreshold={0.2}
            ListFooterComponent={isLoadingMore ? (
                <ActivityIndicator color={theme.colors.primary} size='small' />
            ) : null}
        />
    );
};

const styles = StyleSheet.create({
    listContainer: {
        flexGrow: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    separator: {
        height: 1,
        marginVertical: SPACING.xs,
    },
    loadingIndicator: {
        // paddingVertical: SPACING.md,
        // alignItems: 'center',
        // justifyContent: 'center',
        // alignSelf: 'center',
        // marginVertical: SPACING.md,
        transform: [{ translateX: -50 }, { translateY: -50 }],
        borderWidth: 2,
        // borderColor: 'red',
    },
}); 
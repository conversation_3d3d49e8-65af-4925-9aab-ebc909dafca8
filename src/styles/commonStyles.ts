import { StyleSheet, TextStyle, ViewStyle } from 'react-native';
import {
    getResponsiveFontSize,
    adjustSpacing,
    isAndroid,
    isIOS
} from '@/constants/UI/responsive';
import { FONT_FAMILY, FONT_WEIGHT, SPACING } from '@/constants/UI/themes';
import { AppTheme } from '@/types';


export const createCommonStyles = (theme: AppTheme) => {
    const { colors } = theme;

    return StyleSheet.create({
        // Container styles
        container: {
            flex: 1,
            backgroundColor: colors.background,
        },
        safeAreaContainer: {
            flex: 1,
            backgroundColor: colors.background,
        },
        centerContainer: {
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: colors.background,
        },
        rowContainer: {
            flexDirection: 'row',
            alignItems: 'center',
        },
        spaceBetweenRow: {
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
        },

        // Card styles
        card: {
            backgroundColor: colors.card,
            borderRadius: theme.sizes.borderRadius.medium,
            padding: adjustSpacing(SPACING.md),
            marginVertical: adjustSpacing(SPACING.sm),
            elevation: 2,
            shadowColor: colors.black,
            shadowOffset: { width: 0, height: 1 },
            shadowOpacity: 0.1,
            shadowRadius: 2,
        },

        // Text styles
        heading1: {
            fontSize: getResponsiveFontSize(theme.fonts.titleMedium.fontSize * 1.5),
            fontFamily: FONT_FAMILY.bold,
            color: colors.text,
            marginBottom: adjustSpacing(SPACING.sm),
        } as TextStyle,
        heading2: {
            fontSize: getResponsiveFontSize(theme.fonts.titleMedium.fontSize * 1.2),
            fontFamily: FONT_FAMILY.bold,
            color: colors.text,
            marginBottom: adjustSpacing(SPACING.sm),
        } as TextStyle,
        bodyText: {
            fontSize: getResponsiveFontSize(theme.fonts.bodyMedium.fontSize),
            fontFamily: FONT_FAMILY.regular,
            color: colors.text,
            marginBottom: adjustSpacing(SPACING.xs),
        } as TextStyle,
        smallText: {
            fontSize: getResponsiveFontSize(theme.fonts.bodySmall.fontSize),
            fontFamily: FONT_FAMILY.regular,
            color: colors.text,
        } as TextStyle,
        errorText: {
            fontSize: getResponsiveFontSize(theme.fonts.bodySmall.fontSize),
            fontFamily: FONT_FAMILY.regular,
            color: colors.error,
            marginTop: adjustSpacing(SPACING.xs / 2),
            ...(isAndroid && {
                lineHeight: theme.fonts.bodySmall.fontSize * 1.2,
            }),
        } as TextStyle,

        // Button styles
        primaryButton: {
            backgroundColor: colors.primary,
            borderRadius: theme.sizes.borderRadius.medium,
            paddingVertical: adjustSpacing(SPACING.sm),
            paddingHorizontal: adjustSpacing(SPACING.md),
            alignItems: 'center',
            justifyContent: 'center',
            height: isAndroid ? theme.sizes.buttonHeight * 0.9 : theme.sizes.buttonHeight,
            marginVertical: adjustSpacing(SPACING.sm),
        } as ViewStyle,
        primaryButtonText: {
            color: colors.onPrimary,
            fontFamily: FONT_FAMILY.bold,
            fontSize: getResponsiveFontSize(theme.fonts.bodyLarge.fontSize),
        } as TextStyle,
        secondaryButton: {
            backgroundColor: 'transparent',
            borderRadius: theme.sizes.borderRadius.medium,
            borderWidth: 1,
            borderColor: colors.primary,
            paddingVertical: adjustSpacing(SPACING.sm),
            paddingHorizontal: adjustSpacing(SPACING.md),
            alignItems: 'center',
            justifyContent: 'center',
            height: isAndroid ? theme.sizes.buttonHeight * 0.9 : theme.sizes.buttonHeight,
            marginVertical: adjustSpacing(SPACING.sm),
        } as ViewStyle,
        secondaryButtonText: {
            color: colors.primary,
            fontFamily: FONT_FAMILY.bold,
            fontSize: getResponsiveFontSize(theme.fonts.bodyLarge.fontSize),
        } as TextStyle,
        linkButton: {
            paddingVertical: adjustSpacing(SPACING.xs),
            alignItems: 'center',
            marginVertical: adjustSpacing(SPACING.xs),
            minHeight: 30, // Đảm bảo luôn có chiều cao tối thiểu để không bị biến mất
        } as ViewStyle,
        linkButtonText: {
            color: colors.primary,
            fontFamily: FONT_FAMILY.regular,
            fontWeight: FONT_WEIGHT.medium,
            fontSize: getResponsiveFontSize(theme.fonts.bodyMedium.fontSize),
            textDecorationLine: 'underline',
        } as TextStyle,

        // Input styles
        inputContainer: {
            marginBottom: adjustSpacing(SPACING.md),
        } as ViewStyle,
        labelContainer: {
            flexDirection: 'row',
            marginBottom: adjustSpacing(SPACING.xs),
        } as ViewStyle,
        inputLabel: {
            fontFamily: FONT_FAMILY.regular,
            fontWeight: FONT_WEIGHT.medium,
            fontSize: getResponsiveFontSize(theme.fonts.bodyMedium.fontSize),
            color: colors.text,
        } as TextStyle,
        input: {
            backgroundColor: colors.surface,
            borderRadius: theme.sizes.borderRadius.small,
            height: isAndroid ? theme.sizes.inputHeight * 0.9 : theme.sizes.inputHeight,
            paddingHorizontal: adjustSpacing(SPACING.sm),
            paddingVertical: 0, // Giúp giải quyết vấn đề về chiều cao trên Android
            fontFamily: FONT_FAMILY.regular,
            fontSize: getResponsiveFontSize(theme.fonts.bodyLarge.fontSize),
            color: colors.text,
        } as TextStyle,

        // Utility styles
        divider: {
            height: 1,
            backgroundColor: colors.border,
            marginVertical: adjustSpacing(SPACING.md),
        } as ViewStyle,
        shadow: {
            elevation: 3,
            shadowColor: colors.black,
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 3,
        } as ViewStyle,
        required: {
            color: colors.error,
            marginLeft: 4,
        } as TextStyle,

        // Form section styles
        formSection: {
            marginBottom: adjustSpacing(SPACING.xl),
        } as ViewStyle,
        formContainer: {
            paddingHorizontal: adjustSpacing(SPACING.lg),
            width: '100%',
        } as ViewStyle,
    });
};

/**
 * Tạo các styles form responsive cho từng nền tảng
 * @param theme Theme hiện tại của ứng dụng
 * @returns Object chứa các styles đặc biệt cho form
 */
export const createFormStyles = (theme: AppTheme) => {
    const { colors } = theme;
    const commonStyles = createCommonStyles(theme);

    return {
        ...commonStyles,
        // Các styles dành riêng cho form
        formFieldSpacing: {
            marginBottom: isAndroid ? adjustSpacing(SPACING.md) : SPACING.xl,
        } as ViewStyle,
        errorContainer: {
            marginTop: isAndroid ? 2 : adjustSpacing(SPACING.xs),
        } as ViewStyle,
        // Đảm bảo nút luôn hiển thị ngay cả khi có lỗi
        actionButtonContainer: {
            marginTop: adjustSpacing(SPACING.md),
            paddingBottom: isAndroid ? adjustSpacing(SPACING.md) : 0,
        } as ViewStyle,
    };
}; 
**Android**
<!-- <PERSON>ác file cần cập nhật khi thay đổi tên ứng dụng -->
thay đổi thông tin ứng dụng trong /.env, /.env.staging, /.env.production:
APP_ID=com.mynewapp
APP_NAME=MyNewApp


<!-- thay đổi namespace tại android/app/build.gradle: -->
namespace "com.mynewapp"
<!-- thay đổi namespace tại android/app/src/main/AndroidManifest.xml -->
package="com.mynewapp"
<!-- thay đổi namespace tại android/app/src/main/java/com/ARC/MainActivity.kt-->
package com.mynewapp
getMainComponentName() = "MyNewApp"

<!-- Di chuyển file vào com/mynewapp. -->
android/app/src/main/java/com/ARC/MainApplication.kt:
package com.mynewapp
Di chuyển file vào com/mynewapp.
android/app/src/main/res/values/strings.xml (nếu không dùng APP_NAME động):
<string name="app_name">MyNewApp</string>
android/app/src/main/res/mipmap/ic_launcher.png (và các file biểu tượng khác):
Cập nhật biểu tượng nếu cần.
android/app/proguard-rules.pro (nếu có):
Cập nhật các rule liên quan đến package name.

<!-- Cập nhật React Native (phần JavaScript) -->
/app.json:
name: "MyNewApp"
displayName: "MyNewApp"
/index.js:
AppRegistry.registerComponent('MyNewApp', ...)
/package.json:
name: "mynewapp"




**IOS**
<!-- Lấy đường dẫn root, cập nhật các pre-action trong các scheme -->
<!-- Sao chép, đổi tên thư mục mẫu thành tên ứng dụng mới trong ios  -->
<!-- Kiểm tra appName trong /Users/<USER>/Documents/GitHub/InitProject/index.js đã đúng tên dự án chưa -->
<!-- Cập nhật các tên biến cũ -->
<!-- Kiểm tra file trong đường dẫn /Users/<USER>/Documents/GitHub/InitProject/ios/tmp.xcconfig có lưu đúng giá trị của môi trường đang chạy chưa -->
export interface Customer {
    name: string;
    phone: string;
    address: string;
}

export interface Task {
    task_id: number;
    task_code: string;
    task_type_icon_url: string;
    status: string;
    scheduled_from: string;
    scheduled_to: string;
    customer: Customer;
    products?: string[];
    repair_product?: string;
}

export interface TaskListData {
    status: string;
    total: number;
    limit: number;
    offset: number;
    has_more: boolean;
    records: Task[];
} 
import AsyncStorage from '@react-native-async-storage/async-storage';


export const storeString = async (key: string, value: string): Promise<boolean> => {
    try {
        await AsyncStorage.setItem(key, value);
        return true;
    } catch (error) {
        console.error(`Error storing ${key}:`, error);
        return false;
    }
};


export const getString = async (key: string): Promise<string | null> => {
    try {
        return await AsyncStorage.getItem(key);
    } catch (error) {
        console.error(`Error retrieving ${key}:`, error);
        return null;
    }
};


export const storeObject = async (key: string, value: any): Promise<boolean> => {
    try {
        const jsonValue = JSON.stringify(value);
        await AsyncStorage.setItem(key, jsonValue);
        return true;
    } catch (error) {
        console.error(`Error storing object ${key}:`, error);
        return false;
    }
};


export const getObject = async <T = any>(key: string): Promise<T | null> => {
    try {
        const jsonValue = await AsyncStorage.getItem(key);
        return jsonValue != null ? JSON.parse(jsonValue) as T : null;
    } catch (error) {
        console.error(`Error retrieving object ${key}:`, error);
        return null;
    }
};


export const removeItem = async (key: string): Promise<boolean> => {
    try {
        await AsyncStorage.removeItem(key);
        return true;
    } catch (error) {
        console.error(`Error removing ${key}:`, error);
        return false;
    }
};


export const hasKey = async (key: string): Promise<boolean> => {
    try {
        const value = await AsyncStorage.getItem(key);
        return value !== null;
    } catch (error) {
        console.error(`Error checking key ${key}:`, error);
        return false;
    }
};


export const removeMultiple = async (keys: string[]): Promise<boolean> => {
    try {
        await AsyncStorage.multiRemove(keys);
        return true;
    } catch (error) {
        console.error('Error removing multiple keys:', error);
        return false;
    }
};


export const clearAll = async (): Promise<boolean> => {
    try {
        await AsyncStorage.clear();
        return true;
    } catch (error) {
        console.error('Error clearing AsyncStorage:', error);
        return false;
    }
};
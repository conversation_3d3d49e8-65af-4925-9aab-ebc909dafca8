import React, { useEffect, useState } from 'react';
import { View, Text, Button, Platform, Alert, StyleSheet, Linking } from 'react-native';
import { checkNotifications, requestNotifications, PERMISSIONS, RESULTS } from 'react-native-permissions';
// import messaging from '@react-native-firebase/messaging';

function NotificationPermission() {
    const [permissionStatus, setPermissionStatus] = useState('Chưa kiểm tra');

    // Hàm kiểm tra trạng thái quyền thông báo
    const checkPermision = () => {
        checkNotifications().then(({ status, settings }) => {

            setPermissionStatus(status);

            if (status === 'denied') {
                Alert.alert(
                    'Quyền thông báo bị từ chối',
                    'Ứng dụng cần quyền thông báo để gửi thông báo về lịch hẹn và trạng thái dịch vụ. Bạn có muốn yêu cầu lại quyền này không?',
                    [
                        { text: 'Hủy', style: 'cancel' },
                        { text: 'Yêu cầu lại', onPress: requestNotificationPermission },
                    ]
                );
            } else if (status === 'blocked') {
                Alert.alert(
                    'Quyền thông báo bị chặn',
                    'Quyền thông báo đã bị chặn. Vui lòng vào cài đặt để cấp quyền.',
                    [
                        { text: 'Hủy', style: 'cancel' },
                        { text: 'Mở cài đặt', onPress: () => Linking.openSettings() },
                    ]
                );
            }
        });
    };

    // Hàm yêu cầu quyền thông báo
    const requestNotificationPermission = async () => {
        try {
            // Yêu cầu quyền thông báo
            const result = await requestNotifications(['alert', 'badge', 'sound']);
            const status = result.status;

            if (status === 'granted') {
                // Quyền được cấp, đăng ký nhận thông báo với Firebase
                // const authStatus = await messaging().requestPermission();
                // const enabled =
                //   authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
                //   authStatus === messaging.AuthorizationStatus.PROVISIONAL;
                setPermissionStatus('Đã cấp quyền');
                // if (enabled) {
                //   setPermissionStatus('Đã cấp quyền');
                //   Alert.alert('Thành công', 'Quyền nhận thông báo đã được cấp.');
                //   const token = await messaging().getToken();
                //   console.log('FCM Token:', token);
                // } else {
                //   setPermissionStatus('Bị từ chối');
                //   Alert.alert('Quyền bị từ chối', 'Ứng dụng không có quyền gửi thông báo.');
                // }
            } else if (status === 'denied') {
                setPermissionStatus('Bị từ chối');
                Alert.alert('Quyền bị từ chối', 'Bạn đã từ chối quyền gửi thông báo. Vui lòng cấp quyền trong cài đặt.');
            } else if (status === 'blocked') {
                setPermissionStatus('Bị chặn');
                Alert.alert(
                    'Quyền bị chặn',
                    'Quyền gửi thông báo đã bị chặn. Vui lòng vào cài đặt để cấp quyền.',
                    [
                        { text: 'Hủy', style: 'cancel' },
                        { text: 'Mở cài đặt', onPress: () => Linking.openSettings() },
                    ]
                );
            } else {
                setPermissionStatus('Không khả dụng');
                Alert.alert('Quyền không khả dụng', 'Quyền này không khả dụng trên thiết bị của bạn.');
            }
        } catch (error: any) {
            console.log(error);
            setPermissionStatus('Lỗi');
            Alert.alert('Lỗi', 'Đã có lỗi xảy ra khi yêu cầu quyền thông báo: ' + error.message);
        }
    };

    // Kiểm tra quyền khi component được mount
    useEffect(() => {
        checkPermision();
    }
        , []
    );

    // Xử lý thông báo khi ứng dụng đang chạy
    //   useEffect(() => {
    //     const unsubscribe = messaging().onMessage(async (remoteMessage) => {
    //       Alert.alert('Thông báo mới', remoteMessage.notification.body);
    //     });

    //     return unsubscribe;
    //   }, []);

    // Xử lý thông báo khi ứng dụng ở background
    //   useEffect(() => {
    //     messaging().setBackgroundMessageHandler(async (remoteMessage) => {
    //       console.log('Thông báo ở background:', remoteMessage);
    //     });
    //   }, []);

    return (
        <View style={styles.container}>
            <Text style={styles.title}>Yêu cầu quyền thông báo</Text>
            <Text style={styles.status}>Trạng thái: {permissionStatus}</Text>
            {permissionStatus === 'denied' && (
                <Button title="Yêu cầu lại quyền thông báo" onPress={requestNotificationPermission} />
            )}
            {permissionStatus === 'blocked' && (
                <Button title="Mở cài đặt để cấp quyền" onPress={() => Linking.openSettings()} />
            )}
            {permissionStatus === 'granted' && (
                <Text style={styles.success}>Bạn đã cấp quyền thông báo!</Text>
            )}
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: 20,
        backgroundColor: '#f5f5f5',
        justifyContent: 'center',
        alignItems: 'center',
    },
    title: {
        fontSize: 20,
        fontWeight: 'bold',
        marginBottom: 20,
        color: '#2d4150',
    },
    status: {
        fontSize: 16,
        marginBottom: 20,
        color: '#2d4150',
    },
    success: {
        fontSize: 16,
        color: 'green',
        marginTop: 10,
    },
});

export default NotificationPermission;
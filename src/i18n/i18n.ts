import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import en from './translations/en';
import vi from './translations/vi';
import AsyncStorage from '@react-native-async-storage/async-storage';

const LANGUAGE_KEY = '@app_language';

const resources = {
    en: {
        translation: en
    },
    vi: {
        translation: vi
    }
};

const getStoredLanguage = async () => {
    try {
        const language = await AsyncStorage.getItem(LANGUAGE_KEY);
        return language || 'en';
    } catch (error) {
        console.error('Error reading language from storage:', error);
        return 'en';
    }
};

// Initialize i18next
const initI18n = async () => {
    const storedLanguage = await getStoredLanguage();

    await i18n
        .use(initReactI18next)
        .init({
            resources,
            lng: storedLanguage,
            fallbackLng: 'en',

            interpolation: {
                escapeValue: false
            },

            defaultNS: 'translation',
            ns: ['translation'],

            react: {
                useSuspense: false
            }
        });
};

// Initialize immediately
initI18n().catch(error => {
    console.error('Error initializing i18n:', error);
});

export default i18n; 
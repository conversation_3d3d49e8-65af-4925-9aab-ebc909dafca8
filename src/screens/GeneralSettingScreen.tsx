import React from 'react';
import {
    StyleSheet,
    View,
    Text,
    TouchableOpacity,
    ScrollView,
    StatusBar,
    Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, useTheme } from '@react-navigation/native';
import { ParamListBase } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { SPACING, COLORS, BORDER_RADIUS, STATUS_BAR, GRADIENT_VALUE } from '@/constants/UI/themes';
import CIcon from '@/components/CIcon';
import { Iback, Iarrow, Isetting, Iprivacy } from '@/constants/UI/icons';
import { useTranslation } from 'react-i18next';
import { APP_INFO } from '@/constants/app';
import { SCREEN_NAMES, PRIVACY_POLICY_URL } from '@/constants/navigation';
import { Linking } from 'react-native';
import { AppTheme } from '@/types';
import LinearGradient from 'react-native-linear-gradient';

type GeneralSettingScreenNavigationProp = NativeStackNavigationProp<ParamListBase>;

// Email address constant
const ADMIN_EMAIL = '<EMAIL>';

const GeneralSettingScreen = () => {
    const navigation = useNavigation<GeneralSettingScreenNavigationProp>();
    const { colors } = useTheme() as unknown as AppTheme;
    const { t } = useTranslation();

    const handleBack = () => {
        navigation.goBack();
    };

    const handlePrivacyPolicy = () => {
        Linking.openURL(PRIVACY_POLICY_URL);
    };

    const handleContactAdmin = () => {
        const mailtoUrl = `mailto:${ADMIN_EMAIL}?subject=Support Request`;

        Linking.canOpenURL(mailtoUrl)
            .then((supported) => {
                if (supported) {
                    return Linking.openURL(mailtoUrl);
                } else {
                    Alert.alert(
                        'Email Not Supported',
                        'Your device doesn\'t have an email client configured.',
                        [{ text: 'OK' }]
                    );
                }
            })
            .catch((err) => {
                console.error('Error opening email client:', err);
                Alert.alert(
                    'Error',
                    'Could not open email client. Please try again later.',
                    [{ text: 'OK' }]
                );
            });
    };

    return (

        <SafeAreaView style={styles.container} edges={['left', 'right']}>
            <LinearGradient
                colors={colors.linearGradient}
                locations={GRADIENT_VALUE}
                style={styles.container}
            >
                <ScrollView style={styles.scrollView}>
                    {/* Menu Items */}
                    <View style={styles.menuSection}>
                        {/* Privacy Policy */}
                        <TouchableOpacity
                            style={styles.menuItem}
                            onPress={handlePrivacyPolicy}
                        >
                            <View style={styles.menuIconContainer}>
                                <CIcon source={Iprivacy} size={30} tintColor={COLORS.light.black} />
                            </View>
                            <Text style={styles.menuText}>Privacy Policy</Text>
                            <CIcon source={Iarrow} size={20} tintColor={COLORS.light.black} style={styles.arrowIcon} />
                        </TouchableOpacity>

                        {/* Contact Admin */}
                        <TouchableOpacity
                            style={styles.menuItem}
                            onPress={handleContactAdmin}
                        >
                            <View style={styles.menuIconContainer}>
                                <CIcon source={Isetting} size={25} tintColor={COLORS.light.black} />
                            </View>
                            <Text style={styles.menuText}>Contact Admin</Text>
                            <CIcon source={Iarrow} size={24} tintColor={COLORS.light.black} style={styles.arrowIcon} />
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </LinearGradient>
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: COLORS.light.background,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: SPACING.md,
        paddingVertical: SPACING.sm,
        backgroundColor: COLORS.light.white,
        borderBottomWidth: 1,
        borderBottomColor: COLORS.light.border,
    },
    backButton: {
        padding: SPACING.xs,
    },
    headerTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: COLORS.light.black,
    },
    emptySpace: {
        width: 24,
    },
    scrollView: {
        flex: 1,
    },
    menuSection: {
        backgroundColor: COLORS.light.white,
        marginTop: SPACING.md,
    },
    menuItem: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: SPACING.md,
        paddingHorizontal: SPACING.md,
        borderBottomWidth: 1,
        borderBottomColor: COLORS.light.border,
    },
    menuIconContainer: {
        marginRight: SPACING.md,
    },
    menuText: {
        flex: 1,
        fontSize: 16,
        color: COLORS.light.text,
    },
    arrowIcon: {
        transform: [{ rotate: '90deg' }],
    },
    versionContainer: {
        alignItems: 'center',
        padding: SPACING.md,
    },
    versionText: {
        fontSize: 14,
        color: COLORS.light.textSecondary,
        marginBottom: SPACING.xs,
    },
    versionLine: {
        width: 50,
        height: 4,
        backgroundColor: COLORS.light.black,
        borderRadius: BORDER_RADIUS.round,
    },
});

export default GeneralSettingScreen; 
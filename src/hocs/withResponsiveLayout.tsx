import React from 'react';
import { Platform, StyleSheet, ViewStyle, StyleProp, Keyboard, Dimensions, TouchableWithoutFeedback } from 'react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import { KeyboardAvoidingView } from 'react-native';
import ResponsiveContainer from '../components/ResponsiveContainer';
import { BASE_STYLE } from '@/constants/UI/responsiveStyles';
import LinearGradient from 'react-native-linear-gradient';
import { GRADIENT_VALUE } from '@/constants/UI/themes';
import { useTheme } from '@react-navigation/native';
import { AppTheme } from '@/types';

interface WithResponsiveLayoutProps {
    containerStyle?: StyleProp<ViewStyle>;
    fluid?: boolean;
    ignoreBottomInset?: boolean;
    keyboardAvoidingEnabled?: boolean;
}

const withResponsiveLayout = <P extends object>(
    WrappedComponent: React.ComponentType<P>
) => {
    const WithResponsiveLayoutComponent: React.FC<P & WithResponsiveLayoutProps> = ({
        containerStyle,
        fluid,
        ignoreBottomInset,
        keyboardAvoidingEnabled = true,
        ...props
    }) => {
        const insets = useSafeAreaInsets();
        const screenHeight = Dimensions.get('window').height;
        // Calculate keyboard offset based on platform and screen size
        const keyboardOffset = Platform.select({
            ios: () => {
                const baseOffset = Math.max(insets.bottom, 40);
                // Add more offset for smaller screens
                if (screenHeight <= 667) { // iPhone 8 and smaller
                    return baseOffset + 40;
                }
                return baseOffset + 20;
            },
            android: () => {
                const baseOffset = Math.max(insets.bottom, 16);
                // Android typically needs less offset due to different keyboard behavior
                return baseOffset + 70;
            },
            default: () => 20,
        })();

        const renderContent = () => (
            <ResponsiveContainer
                style={containerStyle}
                fluid={fluid}
                ignoreBottomInset={ignoreBottomInset}
            >
                <WrappedComponent {...(props as P)} />
            </ResponsiveContainer>
        );

        if (!keyboardAvoidingEnabled) {
            return (
                <SafeAreaView
                    style={[styles.safeArea]}
                    edges={['top', 'left', 'right']}
                >
                    {renderContent()}
                </SafeAreaView>
            );
        }

        return (
            <SafeAreaView
                style={[styles.safeArea]}
                edges={['left', 'right']}
            >
                <KeyboardAvoidingView
                    style={styles.keyboardAvoid}
                    behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                    keyboardVerticalOffset={keyboardOffset}
                    enabled={true}
                >
                    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
                        {renderContent()}
                    </TouchableWithoutFeedback>
                </KeyboardAvoidingView>
            </SafeAreaView>
        );
    };

    WithResponsiveLayoutComponent.displayName = `WithResponsiveLayout(${WrappedComponent.displayName || WrappedComponent.name || 'Component'
        })`;

    return WithResponsiveLayoutComponent;
};

const styles = StyleSheet.create({
    safeArea: {
        flex: BASE_STYLE.flex.flex,
        backgroundColor: 'transparent',
    },
    keyboardAvoid: {
        flex: BASE_STYLE.flex.flex,
    },
});

export default withResponsiveLayout; 
export interface ChangePasswordFormData {
    currentPassword: string;
    newPassword: string;
    confirmPassword: string;
}
export interface User {
    id: string;
    email?: string;
    job_title?: string;
    location_id?: string;
    name?: string;
    phone?: string;
    profileImageUrl?: string;
}

export interface FormChangePassword {
    url: string;
    email: string;
    password: string;
    newPassword: string;
}
#import "SplashScreenViewController.h"

@implementation SplashScreenViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    // Get the logo image view
    UIImageView *logoImageView = [self.view viewWithTag:1];
    
    // Start with 0 opacity
    logoImageView.alpha = 0.0;
    
    // Fade in animation
    [UIView animateWithDuration:0.3 animations:^{
        logoImageView.alpha = 1.0;
    } completion:^(BOOL finished) {
        // Wait for 0.7 seconds then fade out
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.7 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [UIView animateWithDuration:0.3 animations:^{
                logoImageView.alpha = 0.0;
            }];
        });
    }];
}

@end 
import React, { useState, useEffect } from 'react';
import { View, StyleSheet, TouchableOpacity, ActivityIndicator, Alert } from 'react-native';
import { useTheme } from '@react-navigation/native';
import CText from '@/components/CText';
import { PointLocation } from '@/components/Map/MapView';
import MapViewComponent from '@/components/Map/MapView';
import CCard from '@/components/CCard';
import { SPACING } from '@/constants/UI/themes';
import Config from 'react-native-config';
import { check, PERMISSIONS, RESULTS, request } from 'react-native-permissions';
import { Platform } from 'react-native';

// Sample data for Single Map
const singlePointData: PointLocation = {
    id: 'point1',
    latitude: 10.762622,
    longitude: 106.660172,
    title: 'Customer Location',
    description: 'Service appointment',
    customer: {
        name: '<PERSON>',
        phone: '0123456789',
    },
    address: '1234 Nguyen Hue Street, District 1, HCMC',
    taskDetails: {
        'Task ID': 'T-12345',
        'Type': 'Installation',
        'Priority': 'High',
        'Scheduled': '2023-08-15 10:00 AM',
    }
};

// Sample data for Multi Map
const multiPointsData: PointLocation[] = [
    {
        id: 'point1',
        latitude: 10.762622,
        longitude: 106.660172,
        title: 'First Customer',
        description: 'First service appointment',
        customer: {
            name: 'John Smith',
            phone: '0123456789',
        },
        address: '1234 Nguyen Hue Street, District 1, HCMC',
        taskDetails: {
            'Task ID': 'T-12345',
            'Type': 'Installation',
            'Priority': 'High',
        }
    },
    {
        id: 'point2',
        latitude: 10.772622,
        longitude: 106.680172,
        title: 'Second Customer',
        description: 'Second service appointment',
        customer: {
            name: 'Jane Doe',
            phone: '0987654321',
        },
        address: '456 Le Loi Street, District 1, HCMC',
        taskDetails: {
            'Task ID': 'T-12346',
            'Type': 'Maintenance',
            'Priority': 'Medium',
        }
    },
    {
        id: 'point3',
        latitude: 10.782622,
        longitude: 106.670172,
        title: 'Third Customer',
        description: 'Third service appointment',
        customer: {
            name: 'Robert Johnson',
            phone: '0909090909',
        },
        address: '789 Pasteur Street, District 3, HCMC',
        taskDetails: {
            'Task ID': 'T-12347',
            'Type': 'Repair',
            'Priority': 'Low',
        }
    }
];

const MapDemoScreen: React.FC = () => {
    const { colors } = useTheme();
    const [showMultiMap, setShowMultiMap] = useState(false);
    const [userLocation, setUserLocation] = useState<{ latitude: number; longitude: number } | null>(null);
    const [loading, setLoading] = useState(true);
    const [hasLocationPermission, setHasLocationPermission] = useState(false);
    const [apiKeyConfigured, setApiKeyConfigured] = useState(false);

    // Check if the Google Maps API key is configured
    useEffect(() => {
        if (Config.GOOGLE_MAPS_API_KEY && Config.GOOGLE_MAPS_API_KEY !== 'YOUR_GOOGLE_MAPS_API_KEY') {
            setApiKeyConfigured(true);
        } else {
            console.warn('Google Maps API key is not properly configured');
        }
        setLoading(false);
    }, []);

    // Check and request location permissions
    useEffect(() => {
        const checkLocationPermission = async () => {
            try {
                const permission = Platform.OS === 'ios'
                    ? PERMISSIONS.IOS.LOCATION_WHEN_IN_USE
                    : PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION;

                const result = await check(permission);

                if (result === RESULTS.GRANTED) {
                    setHasLocationPermission(true);
                } else if (result === RESULTS.DENIED) {
                    const requestResult = await request(permission);
                    setHasLocationPermission(requestResult === RESULTS.GRANTED);
                } else {
                    setHasLocationPermission(false);
                }
            } catch (error) {
                console.error('Error checking location permission:', error);
                setHasLocationPermission(false);
            } finally {
                setLoading(false);
            }
        };

        checkLocationPermission();
    }, []);

    // Simulating user location for demo purposes
    useEffect(() => {
        if (hasLocationPermission) {
            // Set a fake user location near the first point
            setUserLocation({
                latitude: singlePointData.latitude - 0.005,
                longitude: singlePointData.longitude - 0.005
            });
        }
    }, [hasLocationPermission]);

    const toggleMapType = () => {
        setShowMultiMap(!showMultiMap);
    };

    // Handle when a point is pressed
    const handlePointPress = (point: PointLocation) => {
        Alert.alert(
            point.title || 'Location',
            `Customer: ${point.customer?.name}\nAddress: ${point.address}`,
            [{ text: 'OK' }]
        );
    };

    // Render content based on permissions and API key status
    const renderContent = () => {
        if (loading) {
            return (
                <View style={styles.centerContainer}>
                    <ActivityIndicator size="large" color={colors.primary} />
                    <CText style={{ marginTop: 20 }}>Checking map dependencies...</CText>
                </View>
            );
        }

        if (!apiKeyConfigured) {
            return (
                <View style={styles.centerContainer}>
                    <CText style={styles.errorText}>Google Maps API key is not configured properly.</CText>
                    <CText style={styles.infoText}>
                        Please add a valid API key in your environment configuration.
                    </CText>
                </View>
            );
        }

        if (!hasLocationPermission) {
            return (
                <View style={styles.centerContainer}>
                    <CText style={styles.errorText}>Location permission not granted</CText>
                    <CText style={styles.infoText}>
                        Location permission is required to use the map features.
                    </CText>
                </View>
            );
        }

        // Render the actual map
        return (
            <View style={styles.mapContent}>
                <MapViewComponent
                    points={showMultiMap ? multiPointsData : [singlePointData]}
                    userLocation={userLocation}
                    onPointPress={handlePointPress}
                    route={null} // We're not calculating routes in this simple demo
                />
            </View>
        );
    };

    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <CText style={styles.title}>Map Features Demo</CText>
                <CText style={styles.subtitle}>
                    {showMultiMap
                        ? "Multi-point Map: Shows multiple customer locations"
                        : "Single Map: Shows a specific customer location"}
                </CText>
                <TouchableOpacity
                    style={[styles.toggleButton, { backgroundColor: colors.primary }]}
                    onPress={toggleMapType}
                >
                    <CText style={styles.toggleButtonText}>
                        Switch to {showMultiMap ? "Single" : "Multi"} Map
                    </CText>
                </TouchableOpacity>
            </View>

            <View style={styles.mapContainer}>
                {renderContent()}
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff',
    },
    header: {
        padding: 16,
        backgroundColor: 'rgba(0,0,0,0.05)',
        borderRadius: 8,
        margin: 16,
    },
    title: {
        fontSize: 20,
        fontWeight: 'bold',
        marginBottom: 8,
    },
    subtitle: {
        fontSize: 14,
        marginBottom: 16,
    },
    toggleButton: {
        paddingVertical: 8,
        paddingHorizontal: 16,
        borderRadius: 4,
        alignItems: 'center',
        alignSelf: 'flex-start',
    },
    toggleButtonText: {
        color: 'white',
        fontWeight: '500',
    },
    mapContainer: {
        flex: 1,
        borderRadius: 8,
        overflow: 'hidden',
        marginHorizontal: 16,
        marginBottom: 16,
    },
    mapContent: {
        flex: 1,
    },
    centerContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
    },
    errorText: {
        fontSize: 18,
        fontWeight: 'bold',
        color: 'red',
        marginBottom: 10,
        textAlign: 'center',
    },
    infoText: {
        fontSize: 14,
        textAlign: 'center',
        lineHeight: 20,
    }
});

export default MapDemoScreen; 
import { LoginData } from '@/types';
import { ENDPOINTS } from '@/constants/api';
import * as authUtils from '@/utils/authUtils';
import i18n from '@/i18n/i18n';
import apiClient from '@/services/api/apiClient';
import { ApiResponse, LoginResponse } from '@/types/auth';

//=======================================================
export const login = async (loginData: LoginData): Promise<ApiResponse> => {
    try {
        const response = await apiClient.post<ApiResponse>(ENDPOINTS.auth.login, {
            email: loginData.email,
            password: loginData.password,
            url: loginData.url
        });

        if (!response.success) {
            return {
                ...response,
                message: i18n.t(response.error.detail || 'errors.loginFailed')
            };
        }

        if (response.data && 'access_token' in response.data && 'refresh_token' in response.data) {
            await authUtils.saveToken(
                response.data.access_token as keyof LoginResponse,
                response.data.refresh_token as keyof LoginResponse
            );
        }

        return {
            ...response, message: i18n.t('auth.loginSuccess'),
        };
    } catch (error: any) {
        console.error('Login error:', error);
        return {
            success: false,
            message: error.message || i18n.t('errors.somethingWentWrong')
        };
    }
};
//=======================================================
export const refreshToken = async (refreshToken: string): Promise<ApiResponse> => {
    try {
        // ApiClient will handle automatic token refresh
        // But we can manually call it if needed
        const response = await apiClient.post<any>(ENDPOINTS.auth.login, { refreshToken });

        if (!response.success) {
            return {
                success: false,
                message: response.message || i18n.t('errors.tokenRefreshFailed')
            };
        }

        const { token, refreshToken: newRefreshToken, user } = response.data || {};

        if (token) {
            await authUtils.saveToken(token, newRefreshToken);
        }

        return {
            ...response, message: i18n.t('auth.tokenRefreshed'),
        };
    } catch (error: any) {
        console.error('Refresh token error:', error);
        return {
            success: false,
            message: error.message || i18n.t('errors.somethingWentWrong')
        };
    }
};

//=======================================================
export const logout = async (): Promise<{ success: boolean; message: string; }> => {
    try {
        const response = await apiClient.post<any>(ENDPOINTS.auth.logout);
        await authUtils.clearTokens();

        return {
            ...response as ApiResponse,
            success: response.success,
        };
    } catch (error: any) {
        console.error('Logout error:', error);
        await authUtils.clearTokens();

        return {
            success: false,
            message: error.message || i18n.t('errors.somethingWentWrong')
        };
    }
};

//=======================================================
export const forgotPassword = async (email: string, baseUrl?: string): Promise<{ success: boolean; message: string; }> => {
    try {
        if (baseUrl) {
            apiClient.setBaseUrl(baseUrl);
        }

        const response = await apiClient.post<any>(ENDPOINTS.auth.forgotPassword, { email });

        return {
            success: response.success,
            message: response.message || i18n.t('auth.passwordResetSent')
        };
    } catch (error: any) {
        console.error('Forgot password error:', error);
        return {
            success: false,
            message: error.message || i18n.t('errors.somethingWentWrong')
        };
    }
};


export default {
    login,
    logout,
    refreshToken,
    forgotPassword,
};
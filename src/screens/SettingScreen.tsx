import React, { useState, useEffect, useRef } from 'react';
import {
    StyleSheet,
    View,
    Text,
    TouchableOpacity,
    Switch,
    ScrollView,
    StatusBar,
    Platform,
    Linking,
    Alert,
    AppState,
    AppStateStatus,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, useTheme } from '@react-navigation/native';
import { ParamListBase } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { SPACING, COLORS, BORDER_RADIUS, FONT_WEIGHT, STATUS_BAR, FONT_SIZE, GRADIENT_VALUE } from '@/constants/UI/themes';
import { DEFAULT_AVATAR_URL, DEFAULT_USER_NAME, DEFAULT_USER_ROLE, APP_INFO } from '@/constants/app';
import CIcon from '@/components/CIcon';
import { Isetting, Iarrow, Ichange } from '@/constants/UI/icons';
import ProfileAvatar from '@/components/ProfileAvatar';
import { useBottomSheet } from '@/hocs/BottomSheetContext';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import { usePermission } from '@/hooks/usePermission';
import { useTranslation } from 'react-i18next';
import { SCREEN_NAMES, PRIVACY_POLICY_URL } from '@/constants/navigation';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { getResponsiveFontSize } from '@/constants/UI/responsive';
import { AppTheme } from '@/types';
import LinearGradient from 'react-native-linear-gradient';
import ProfileSection from '@/components/ProfileSection';

const PERMISSIONS_REQUESTED_ONCE_KEY = 'permissions_requested_once';

type SettingScreenNavigationProp = NativeStackNavigationProp<ParamListBase>;

const SettingScreen = () => {
    const navigation = useNavigation<SettingScreenNavigationProp>();
    const { data } = useSelector((state: RootState) => state.user);
    const notificationPermission = usePermission('notification');
    const locationPermission = usePermission('location');
    const { t } = useTranslation();

    const [pushNotifications, setPushNotifications] = useState(false);
    const [allowLocation, setAllowLocation] = useState(false);
    const appState = useRef(AppState.currentState);
    const { colors } = useTheme() as unknown as AppTheme;
    useEffect(() => {
        setPushNotifications(notificationPermission.isGranted);
    }, [notificationPermission.isGranted]);

    useEffect(() => {
        setAllowLocation(locationPermission.isGranted);
    }, [locationPermission.isGranted]);

    useEffect(() => {
        const handleAppStateChange = async (nextAppState: AppStateStatus) => {
            if (appState.current.match(/inactive|background/) && nextAppState === 'active') {

                const notiGranted = await notificationPermission.check();
                setPushNotifications(notiGranted === 'granted');
                const locGranted = await locationPermission.check();
                setAllowLocation(locGranted === 'granted');
            }
            appState.current = nextAppState;
        };
        const subscription = AppState.addEventListener('change', handleAppStateChange);
        return () => subscription.remove();
    }, [notificationPermission, locationPermission]);


    const handleProfilePress = () => {
        navigation.navigate(SCREEN_NAMES.PROFILE);
    };


    const handleTermsPress = () => {
        navigation.navigate(SCREEN_NAMES.GENERAL_SETTING);
    };

    const togglePushNotifications = async () => {
        if (!notificationPermission.isGranted) {

            const requestedOnce = await AsyncStorage.getItem(PERMISSIONS_REQUESTED_ONCE_KEY);

            if (!requestedOnce) {

                await notificationPermission.request();
                await AsyncStorage.setItem(PERMISSIONS_REQUESTED_ONCE_KEY, 'true');
            } else {

                Alert.alert(
                    t('settings.permissionNeededTitle', { defaultValue: 'Permission Needed' }),
                    t('settings.permissionNeededMessage', { defaultValue: 'Please open settings to grant notification permission' }),
                    [
                        { text: t('common.cancel'), style: 'cancel' },
                        { text: t('common.openSettings'), onPress: () => notificationPermission.openSettings() }
                    ]
                );
            }
        } else {

            Alert.alert(
                t('settings.revokeNotificationTitle'),
                t('settings.revokeNotificationMessage'),
                [
                    { text: t('common.cancel'), style: 'cancel' },
                    { text: t('common.openSettings'), onPress: () => notificationPermission.openSettings() }
                ]
            );
        }
    };

    const toggleLocationPermission = async () => {
        if (!locationPermission.isGranted) {

            const requestedOnce = await AsyncStorage.getItem(PERMISSIONS_REQUESTED_ONCE_KEY);

            if (!requestedOnce) {

                await locationPermission.request();
                await AsyncStorage.setItem(PERMISSIONS_REQUESTED_ONCE_KEY, 'true');
            } else {

                Alert.alert(
                    t('settings.permissionNeededTitle', { defaultValue: 'Permission Needed' }),
                    t('settings.permissionNeededMessage', { defaultValue: 'Please open settings to grant location permission' }),
                    [
                        { text: t('common.cancel'), style: 'cancel' },
                        { text: t('common.openSettings'), onPress: () => locationPermission.openSettings() }
                    ]
                );
            }
        } else {

            Alert.alert(
                t('settings.revokeLocationTitle'),
                t('settings.revokeLocationMessage'),
                [
                    { text: t('common.cancel'), style: 'cancel' },
                    { text: t('common.openSettings'), onPress: () => locationPermission.openSettings() }
                ]
            );
        }
    };

    return (
        <SafeAreaView style={styles.container} edges={['left', 'right']}>
            <LinearGradient
                colors={colors.linearGradient}
                locations={GRADIENT_VALUE}
                style={styles.container}
            >
                <ScrollView style={styles.scrollView}>
                    {/* Profile Section */}
                    <ProfileSection data={data} handleProfilePress={handleProfilePress} />

                    {/* General Settings Section */}
                    <View style={styles.sectionContainer}>
                        <Text style={styles.sectionTitle}>{t('settings.generalSettings')}</Text>

                        {/* Push Notification Toggle */}
                        <View style={styles.settingRow}>
                            <Text style={styles.settingLabel}>{t('settings.pushNotification')}</Text>
                            <Switch
                                trackColor={{ false: COLORS.light.border, true: COLORS.light.primary }}
                                thumbColor={COLORS.light.white}
                                ios_backgroundColor={COLORS.light.border}
                                onValueChange={togglePushNotifications}
                                value={pushNotifications}
                            />
                        </View>

                        {/* Allow Location Toggle */}
                        <View style={styles.settingRow}>
                            <Text style={styles.settingLabel}>{t('settings.allowLocation')}</Text>
                            <Switch
                                trackColor={{ false: COLORS.light.border, true: COLORS.light.primary }}
                                thumbColor={COLORS.light.white}
                                ios_backgroundColor={COLORS.light.border}
                                onValueChange={toggleLocationPermission}
                                value={allowLocation}
                            />
                        </View>

                        {/* Terms of Use */}
                        <TouchableOpacity style={styles.termsRow} onPress={handleTermsPress}>
                            <Text style={styles.settingLabel}>{t('settings.termsOfUse', { defaultValue: 'Terms of Use' })}</Text>
                            <View style={styles.arrowIcon}>
                                <CIcon source={Iarrow} size={20} tintColor={COLORS.light.primary} />
                            </View>
                        </TouchableOpacity>

                        {/* Show warning message if permissions are disabled */}
                        {(!pushNotifications || !allowLocation) && (
                            <Text style={styles.warningText}>{t('settings.permissionWarning')}</Text>
                        )}
                    </View>
                </ScrollView>


            </LinearGradient>





        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        // backgroundColor: COLORS.light.background,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: SPACING.md,
        backgroundColor: COLORS.light.white,
        borderBottomWidth: 1,
        borderBottomColor: COLORS.light.border,
    },
    headerTitle: {
        fontSize: 20,
        fontWeight: FONT_WEIGHT.semiBold,
        color: COLORS.light.black,
    },
    emptySpace: {
        width: 40,
    },
    scrollView: {
        flex: 1,
    },
    profileSection: {
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        // backgroundColor: COLORS.light.white,
        paddingVertical: SPACING.lg,
        paddingHorizontal: SPACING.md,
    },
    avatar: {
        width: 64,
        height: 64,
        borderRadius: 32,
        borderWidth: 1,
        borderColor: COLORS.light.black,
    },
    profileInfo: {
        flex: 1,
        alignItems: 'center',

    },
    profileName: {
        fontSize: 18,
        fontWeight: FONT_WEIGHT.semiBold,
        color: COLORS.light.primary,
        marginTop: SPACING.sm,
    },
    profileRole: {
        fontSize: 14,
        color: COLORS.light.textSecondary,
        marginTop: 2,
    },
    settingIcon: {
        position: 'absolute',
        top: SPACING.lg,
        right: SPACING.md,
    },
    sectionContainer: {
        backgroundColor: COLORS.light.white,
        padding: SPACING.md,
        marginTop: SPACING.md,
    },
    sectionTitle: {
        fontSize: 16,
        fontWeight: FONT_WEIGHT.semiBold,
        marginBottom: SPACING.md,
        color: COLORS.light.black,
    },
    settingRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: SPACING.md,
        borderBottomWidth: 1,
        borderBottomColor: COLORS.light.border,
    },
    termsRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: SPACING.md,
    },
    settingLabel: {
        fontSize: getResponsiveFontSize(FONT_SIZE.lg),
        color: COLORS.light.black,
    },
    warningText: {
        fontSize: 14,
        color: '#666',
        marginTop: SPACING.md,
        fontStyle: 'italic',
    },
    versionContainer: {
        alignItems: 'center',
        padding: SPACING.lg,
    },
    versionText: {
        fontSize: 14,
        color: COLORS.light.textSecondary,
        marginBottom: SPACING.sm,
    },
    versionLine: {
        width: 70,
        height: 4,
        backgroundColor: COLORS.light.black,
        borderRadius: BORDER_RADIUS.round,
    },
    arrowIcon: {
        transform: [{ rotate: '90deg' }],
    },
});

export default SettingScreen; 
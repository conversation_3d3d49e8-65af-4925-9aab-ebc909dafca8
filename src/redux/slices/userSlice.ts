import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import i18n from '@/i18n/i18n';
import userService from '@/services/userService';
import { ApiResponse, User } from '@/types';

interface UserState {
    data: Partial<User> | null;
    loading: boolean;
    error: string | null;
    passwordChange: {
        loading: boolean;
        error: string | null;
        success: boolean;
    }
}

const initialState: UserState = {
    data: null,
    loading: false,
    error: null,
    passwordChange: {
        loading: false,
        error: null,
        success: false
    }
};

export const changePassword = createAsyncThunk(
    'user/changePassword',
    async ({ currentPassword, newPassword, confirmPassword }: { currentPassword: string; newPassword: string; confirmPassword: string }, { rejectWithValue }) => {
        try {
            const response: ApiResponse = await userService.changePassword(currentPassword, newPassword, confirmPassword);

            if (!response.success) {
                return rejectWithValue(response.message || i18n.t('errors.passwordChangeFailed'));
            }

            return response;
        } catch (error: any) {
            const errorMessage = error?.message || i18n.t('errors.somethingWentWrong');
            return rejectWithValue(errorMessage);
        }
    }
);

export const getUserInfo = createAsyncThunk(
    'user/getUserInfo',
    async (_, { dispatch, rejectWithValue }) => {
        try {
            const response = await userService.getUserInfo();
            if (!response.success) {
                return rejectWithValue(response.message);
            }
            dispatch(setUser(response.data));
        } catch (error: any) {
            return rejectWithValue(error.message || 'Get user info failed');
        }
    }
);

const userSlice = createSlice({
    name: 'user',
    initialState,
    reducers: {
        resetPasswordState: (state) => {
            state.passwordChange = {
                loading: false,
                error: null,
                success: false
            };
        },
        setUser(state, action: PayloadAction<Partial<User>>) {
            state.data = action.payload;
        }
    },
    extraReducers: (builder) => {
        builder
            // Change Password Cases
            .addCase(changePassword.pending, (state) => {
                state.passwordChange.loading = true;
                state.passwordChange.error = null;
                state.passwordChange.success = false;
            })
            .addCase(changePassword.fulfilled, (state) => {
                state.passwordChange.loading = false;
                state.passwordChange.error = null;
                state.passwordChange.success = true;
            })
            .addCase(changePassword.rejected, (state, action) => {
                state.passwordChange.loading = false;
                state.passwordChange.error = action.payload as string;
                state.passwordChange.success = false;
            })

            // Get User Info Cases
            .addCase(getUserInfo.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getUserInfo.fulfilled, (state) => {
                state.loading = false;
                state.error = null;
            })
            .addCase(getUserInfo.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string;
            });
    }
});

export const { resetPasswordState, setUser } = userSlice.actions;
export default userSlice.reducer;
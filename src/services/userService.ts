import { ENDPOINTS } from '@/constants/api';
import i18n from '@/i18n/i18n';
import apiClient from '@/services/api/apiClient';
import { ApiResponse } from '@/types/auth';

//=======================================================
export const changePassword = async (currentPassword: string, newPassword: string, confirmPassword: string): Promise<{ success: boolean; message: string; }> => {
    try {
        const response = await apiClient.post<ApiResponse>(ENDPOINTS.user.changePassword, {
            current_password: currentPassword,
            new_password: newPassword,
            confirm_password: confirmPassword
        });
        console.log('[userService] changePassword response', response);
        return {
            ...response,
            message: response.message || i18n.t('auth.passwordChanged')
        };
    } catch (error: any) {
        console.error('Change password error:', error);
        return {
            success: false,
            message: error.message || i18n.t('errors.somethingWentWrong')
        };
    }
};

//=======================================================
export const getUserInfo = async (): Promise<ApiResponse> => {
    try {
        const response = await apiClient.get<any>(ENDPOINTS.user.me);

        if (!response.success) {
            return {
                success: false,
                message: response.message || i18n.t('errors.failedToGetUserInfo')
            };
        }

        return {
            ...response, message: response.message || 'User info retrieved successfully',
        };
    } catch (error: any) {
        console.error('Get user info error:', error);
        return {
            success: false,
            message: error.message || i18n.t('errors.somethingWentWrong')
        };
    }
};

export default {
    changePassword,
    getUserInfo
};
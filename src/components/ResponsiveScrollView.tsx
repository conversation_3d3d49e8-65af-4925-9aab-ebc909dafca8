import React from 'react';
import {
    ScrollView,
    ScrollViewProps,
    StyleSheet,
    ViewStyle,
    StyleProp,
    useWindowDimensions,
    Platform,
} from 'react-native';
import { PADDING, SPACING, INSETS } from '@/constants/UI/themes';
import { getBreakpoint, isLandscape } from '@/constants/UI/responsive';
import { DeviceType } from '@/types';

interface ResponsiveScrollViewProps extends ScrollViewProps {
    containerStyle?: StyleProp<ViewStyle>;
    contentContainerStyle?: StyleProp<ViewStyle>;
    withPadding?: boolean;
    withSpacing?: boolean;
    responsive?: boolean;
}

const ResponsiveScrollView: React.FC<ResponsiveScrollViewProps> = ({
    children,
    containerStyle,
    contentContainerStyle,
    withPadding = true,
    withSpacing = true,
    responsive = true,
    ...props
}) => {
    const { width, height } = useWindowDimensions();
    const breakpoint: DeviceType = getBreakpoint(width);
    const landscape = isLandscape(width, height);
    const isMobile = breakpoint === 'mobile' && !landscape;

    const getResponsiveStyle = (): ViewStyle => {
        if (!responsive) return {};

        return {
            flexDirection: isMobile ? 'column' : 'row',
            flexWrap: isMobile ? 'nowrap' : 'wrap',
            gap: withSpacing ? SPACING.md : 0,
        };
    };

    return (
        <ScrollView
            style={[styles.container, containerStyle]}
            contentContainerStyle={[
                styles.contentContainer,
                withPadding && styles.withPadding,
                getResponsiveStyle(),
                contentContainerStyle,
            ]}
            showsVerticalScrollIndicator={false}
            bounces={true}
            overScrollMode="always"
            keyboardShouldPersistTaps="handled"
            keyboardDismissMode="on-drag"
            {...props}
        >
            {children}
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    contentContainer: {
        flexGrow: 1,
        width: '100%',
        paddingBottom: Platform.OS === 'android' ? INSETS.navigation.android : 0,
    },
    withPadding: {
        padding: PADDING.md,
    },
});

export default ResponsiveScrollView; 
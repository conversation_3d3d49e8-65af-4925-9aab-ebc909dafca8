import React, { useState, useMemo } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Calendar as RNCalendar } from 'react-native-calendars';
import { format, startOfWeek, addDays } from 'date-fns';
import { vi } from 'date-fns/locale';
import { SPACING, COLORS } from '@/constants/UI/themes';
import { DateHeader } from './DateHeader';

interface CalendarProps {
    onDateSelect?: (date: Date) => void;
}

export const Calendar: React.FC<CalendarProps> = ({ onDateSelect }) => {
    const [selectedDate, setSelectedDate] = useState(new Date());
    const [showCalendar, setShowCalendar] = useState(false);

    const weekDays = useMemo(() => {
        const start = startOfWeek(selectedDate, { weekStartsOn: 1 }); // Bắt đầu từ thứ 2
        return Array.from({ length: 7 }, (_, i) => addDays(start, i));
    }, [selectedDate]);

    const handleDayPress = (day: any) => {
        const newDate = new Date(day.timestamp);
        setSelectedDate(newDate);
        setShowCalendar(false);
        onDateSelect?.(newDate);
    };

    const handleWeekdayPress = (date: Date) => {
        setSelectedDate(date);
        onDateSelect?.(date);
    };

    const toggleCalendar = () => {
        setShowCalendar(!showCalendar);
    };

    return (
        <View style={styles.container}>
            <DateHeader
                date={selectedDate}
                onPress={toggleCalendar}
                containerStyle={styles.headerContainer}
            />

            {/* Calendar */}
            {showCalendar && (
                <RNCalendar
                    current={format(selectedDate, 'yyyy-MM-dd')}
                    onDayPress={handleDayPress}
                    markedDates={{
                        [format(selectedDate, 'yyyy-MM-dd')]: {
                            selected: true,
                            selectedColor: COLORS.light.primary,
                        },
                    }}
                    theme={{
                        todayTextColor: COLORS.light.primary,
                        selectedDayBackgroundColor: COLORS.light.primary,
                        arrowColor: COLORS.light.primary,
                    }}
                />
            )}

            {/* Week Days */}
            <View style={styles.weekContainer}>
                {weekDays.map((date, index) => (
                    <TouchableOpacity
                        key={index}
                        style={styles.dayContainer}
                        onPress={() => handleWeekdayPress(date)}
                    >
                        <Text style={styles.dayName}>
                            {format(date, 'EEE', { locale: vi })}
                        </Text>
                        <Text style={[
                            styles.dayNumber,
                            format(date, 'yyyy-MM-dd') === format(selectedDate, 'yyyy-MM-dd') && styles.selectedDay
                        ]}>
                            {format(date, 'd')}
                        </Text>
                    </TouchableOpacity>
                ))}
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        backgroundColor: COLORS.light.white,
        borderRadius: 8,
        padding: SPACING.md,
    },
    headerContainer: {
        marginBottom: SPACING.md,
    },
    weekContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: SPACING.md,
        paddingTop: SPACING.md,
        borderTopWidth: 1,
        borderTopColor: COLORS.light.border,
    },
    dayContainer: {
        alignItems: 'center',
        width: 40,
    },
    dayName: {
        fontSize: 12,
        color: COLORS.light.text,
        marginBottom: SPACING.xs,
    },
    dayNumber: {
        fontSize: 16,
        color: COLORS.light.text,
    },
    selectedDay: {
        color: COLORS.light.primary,
        fontWeight: 'bold',
    },
}); 
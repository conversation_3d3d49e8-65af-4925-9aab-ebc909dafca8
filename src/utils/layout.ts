import { useWindowDimensions } from 'react-native';
import { DEVICES_MAXWIDTH } from '../constants/UI/themes';
import { getBreakpoint } from '../constants/UI/responsive';
import { DeviceType } from '../types';

/**
 * Get maximum width for a container based on device type
 * @param breakpoint Current device breakpoint
 * @param currentWidth Current screen width
 * @returns Maximum width for the container
 */
export const getMaxWidth = (breakpoint: DeviceType, currentWidth: number): number => {
    return typeof DEVICES_MAXWIDTH[breakpoint] === 'number'
        ? DEVICES_MAXWIDTH[breakpoint] as number
        : currentWidth;
};

/**
 * Hook to get current maximum width based on screen size
 * @returns Maximum width for the current screen
 */
export const useMaxWidth = (): number => {
    const { width } = useWindowDimensions();
    const breakpoint = getBreakpoint(width);
    return getMaxWidth(breakpoint, width);
}; 
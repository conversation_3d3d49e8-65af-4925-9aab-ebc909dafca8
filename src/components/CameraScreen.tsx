import React, { useState } from 'react';
import {
    View,
    StyleSheet,
    Text,
    TouchableOpacity,
    Alert,
    Platform,
    Image,
    FlatList
} from 'react-native';
import { CameraRoll, PhotoIdentifier } from '@react-native-camera-roll/camera-roll';
import { useBottomSheet } from '../hocs/BottomSheetContext';
import { createCameraTakePhotoScreenStyles } from '@/styles/cameraTakePhotoScreen';
import CIcon from './CIcon';
import { Icamera } from '@/constants/UI/icons';
import { useTheme } from '@react-navigation/native';
import { CodeType } from 'react-native-vision-camera';
import CameraBase from './CameraBase';

interface CameraScreenProps {
    isScan?: boolean;
    onCodeScanned?: (code: string) => void;
    onPhotoTaken?: (uri: string) => void;
    onClose?: () => void;
}

const SCAN_CODE_TYPES: CodeType[] = ['qr', 'ean-13', 'code-128', 'ean-8'];

const CameraScreen: React.FC<CameraScreenProps> = (props) => {
    const { isScan = false, onCodeScanned, onPhotoTaken, onClose } = props;
    const [photos, setPhotos] = useState<PhotoIdentifier[]>([]);
    const [selectedPhotos, setSelectedPhotos] = useState<string[]>([]);
    const [isMultiSelect, setIsMultiSelect] = useState(false);
    const [photoPath, setPhotoPath] = useState<string | null>(null);
    const [storagePermissionGranted, setStoragePermissionGranted] = useState<boolean | null>(null);
    const { openBottomSheet, isBottomSheetOpen } = useBottomSheet();
    const theme = useTheme();
    const styles = createCameraTakePhotoScreenStyles();

    interface BottomSheetPhotoListProps {
        photos: PhotoIdentifier[];
        selectedPhotos: string[];
        togglePhotoSelection: (uri: string) => void;
        deletePhoto: (uri: string) => void;
        storagePermissionGranted: boolean | null;
        requestPermissions: () => void;
    }

    const BottomSheetPhotoList = ({
        photos,
        selectedPhotos,
        togglePhotoSelection,
        deletePhoto,
        storagePermissionGranted,
        requestPermissions,
    }: BottomSheetPhotoListProps) => {
        const deleteSelectedPhotos = async () => {
            if (selectedPhotos.length === 0) {
                Alert.alert('Notice', 'Please select at least one photo to delete.');
                return;
            }
            Alert.alert(
                'Confirm',
                `Are you sure you want to delete ${selectedPhotos.length} selected photos?`,
                [
                    { text: 'Cancel', style: 'cancel' },
                    {
                        text: 'Delete',
                        style: 'destructive',
                        onPress: async () => {
                            try {
                                await CameraRoll.deletePhotos(selectedPhotos);
                                setPhotos(photos.filter((photo) => !selectedPhotos.includes(photo.node.image.uri)));
                                setSelectedPhotos([]);
                                if (isBottomSheetOpen) {
                                    handleOpen();
                                }
                            } catch (error) {
                                console.error('Error deleting photos:', error);
                                Alert.alert('Error', 'Unable to delete photos. Please try again.');
                            }
                        },
                    },
                ]
            );
        };

        const takeSelectedPhotos = async () => {
            if (selectedPhotos.length === 0) {
                Alert.alert('Notice', 'Please select at least one photo.');
                return;
            }
            Alert.alert(
                'Confirm',
                `Are you sure you want to retrieve ${selectedPhotos.length} selected photos?`,
                [
                    { text: 'Cancel', style: 'destructive' },
                    {
                        text: 'Retrieve',
                        style: 'default',
                        onPress: async () => {
                            try {
                                // Retrieve func
                                // await retrievePhotos(selectedPhotos);

                                setSelectedPhotos([]);
                                if (isBottomSheetOpen) {
                                    handleOpen();
                                }
                            } catch (error) {
                                console.error('Error retrieving photos:', error);
                                Alert.alert('Error', 'Unable to retrieve photos. Please try again.');
                            }
                        },
                    },
                ]
            );
        };

        if (!storagePermissionGranted) {
            return (
                <View style={styles.modalError}>
                    <TouchableOpacity style={styles.button} onPress={requestPermissions}>
                        <Text style={styles.buttonText}>Request Permissions</Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={styles.button} onPress={openSettings}>
                        <Text style={styles.buttonText}>Open Settings</Text>
                    </TouchableOpacity>
                </View>
            );
        }

        return (
            <View style={styles.modalContent}>
                <View style={styles.modalHeader}>
                    <Text style={styles.modalTitle}>
                        {selectedPhotos.length > 0
                            ? `${selectedPhotos.length} photos selected`
                            : 'Select Photos'}
                    </Text>
                    <View style={styles.headerActions}>
                        <TouchableOpacity onPress={deleteSelectedPhotos} disabled={selectedPhotos.length === 0}>
                            {/* <Icon source="trash-can" size={30} color={selectedPhotos.length != 0 ? styles.deleteButton.color : styles.disabledButton.color} /> */}
                        </TouchableOpacity>
                        <TouchableOpacity onPress={takeSelectedPhotos} disabled={selectedPhotos.length === 0}>
                            {/* <Icon source="check" size={30} color={selectedPhotos.length != 0 ? styles.activeButton.color : styles.disabledButton.color} /> */}
                        </TouchableOpacity>
                    </View>
                </View>
                <FlatList
                    data={photos}
                    keyExtractor={(item) => item.node.image.uri}
                    numColumns={3}
                    renderItem={({ item }) => {
                        const uri = item.node.image.uri;
                        const isSelected = selectedPhotos.includes(uri);
                        return (
                            <View style={styles.photoContainer}>
                                <TouchableOpacity
                                    onPress={() => togglePhotoSelection(uri)}
                                >
                                    <Image source={{ uri }} style={styles.photo} />
                                    {isSelected && (
                                        <View style={styles.selectedOverlay}>
                                            <Text style={styles.checkmark}>✓</Text>
                                        </View>
                                    )}
                                </TouchableOpacity>
                            </View>
                        );
                    }}
                />
            </View>
        );
    };

    const openSettings = async () => {
        try {
            const { Linking } = require('react-native');
            await Linking.openSettings();
        } catch (error) {
            console.error('Error opening settings:', error);
            Alert.alert('Error', 'Unable to open settings. Please grant permissions manually in device settings.');
        }
    };

    const requestPermissions = async () => {
        // This function is now handled inside CameraBase
        // We'll just return true for compatibility with existing code
        return true;
    };

    const loadPhotos = async () => {
        try {
            const result = await CameraRoll.getPhotos({
                first: 20,
                assetType: 'Photos',
            });
            setPhotos(result.edges);
        } catch (error) {
            console.error('Error loading photos from gallery:', error);
        }
    };

    const deletePhoto = async (uri: string) => {
        if (storagePermissionGranted !== true) {
            Alert.alert(
                'Storage Permission',
                'Storage permission not granted. Please grant permission in settings.',
                [
                    { text: 'Cancel', style: 'cancel' },
                    { text: 'Open Settings', onPress: openSettings },
                ]
            );
            return;
        }

        try {
            await CameraRoll.deletePhotos([uri]);
            const updatedPhotos = photos.filter((photo) => photo.node.image.uri !== uri);
            setPhotos(updatedPhotos);
            setSelectedPhotos(selectedPhotos.filter((selectedUri) => selectedUri !== uri));
            if (isBottomSheetOpen) {
                handleOpen();
            }
        } catch (error) {
            console.error('Error deleting photo:', error);
            Alert.alert('Error', 'Unable to delete photo. Please try again.');
        }
    };

    const togglePhotoSelection = (uri: string) => {
        if (storagePermissionGranted !== true) {
            Alert.alert(
                'Storage Permission',
                'Storage permission not granted. Please grant permission in settings.',
                [
                    { text: 'Cancel', style: 'cancel' },
                    { text: 'Open Settings', onPress: openSettings },
                ]
            );
            return;
        }

        setSelectedPhotos((prev) => {
            if (isMultiSelect) {
                return prev.includes(uri) ? prev.filter((u) => u !== uri) : [...prev, uri];
            } else {
                return prev.includes(uri) ? [] : [uri];
            }
        });
    };

    const toggleMultiSelect = () => {
        setIsMultiSelect((prev) => !prev);
        setSelectedPhotos([]);
    };

    const handlePhotoTaken = (uri: string) => {
        setPhotoPath(uri);
        loadPhotos();
        if (onPhotoTaken) {
            onPhotoTaken(uri);
        }
    };

    const handleOpen = () => {
        openBottomSheet({
            content: (
                <BottomSheetPhotoList
                    photos={photos}
                    selectedPhotos={selectedPhotos}
                    togglePhotoSelection={togglePhotoSelection}
                    deletePhoto={deletePhoto}
                    storagePermissionGranted={storagePermissionGranted}
                    requestPermissions={requestPermissions}
                />
            ),
            snapPoints: ['50%', '80%'],
        });
    };

    return (
        <View style={styles.container}>
            <CameraBase
                onCodeScanned={onCodeScanned}
                onPhotoTaken={handlePhotoTaken}
                onClose={onClose}
                isScanMode={isScan}
                codeTypes={SCAN_CODE_TYPES}
            />

            {isScan && (
                <View style={styles.secondaryControls}>
                    <TouchableOpacity onPress={toggleMultiSelect} style={styles.secondaryButton}>
                        <Text style={styles.buttonText}>
                            {isMultiSelect ? 'Select Single Photo' : 'Select Multiple Photos'}
                        </Text>
                    </TouchableOpacity>
                </View>
            )}
        </View>
    );
};

export default CameraScreen;

import React, { useState } from 'react';
import {
    StyleSheet,
    View,
    Text,
    TouchableOpacity,
    Image,
    StatusBar,
    Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, useRoute, useTheme, RouteProp } from '@react-navigation/native';
import { Task } from '@/interfaces/task';
import { ProductItem } from '@/interfaces/product';
import { SPACING, COLORS, BORDER_RADIUS, INSETS } from '@/constants/UI/themes';
import { format } from 'date-fns';
import CIcon from '@/components/CIcon';
import {
    Iback,
    Iphone,
    Isetting,
    Icalender,
    Iperson,
    Imap,
    Iclose
} from '@/constants/UI/icons';
import { mockProductItems } from '@/constants/mock/products';
import { mockNotes } from '@/constants/mock/notes';
import ResponsiveScrollView from '@/components/ResponsiveScrollView';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

type TaskDetailRouteProp = RouteProp<{ TaskDetail: { task: Task } }, 'TaskDetail'>;

const TaskDetailScreen = () => {
    const navigation = useNavigation();
    const route = useRoute<TaskDetailRouteProp>();
    const { task } = route.params;
    const { colors } = useTheme();
    const [activeTab, setActiveTab] = useState<'internal' | 'external'>('internal');
    const insets = useSafeAreaInsets();

    // Format date from task
    const formatDateTime = (dateString: string) => {
        const date = new Date(dateString);
        return `${format(date, 'MM/dd/yyyy')} • ${format(date, 'hh:mm a')}`;
    };

    // Extract task type from task code
    const getTaskType = (taskCode: string) => {
        if (taskCode.includes('DROP')) return 'Drop Off';
        if (taskCode.includes('INSTALLATION')) return 'Installation';
        if (taskCode.includes('DELIVERY')) return 'Delivery and Installation';
        if (taskCode.includes('SERVICE')) return 'Service';
        if (taskCode.includes('REPAIR')) return 'Repair';
        return 'Delivery and Installation'; // Default
    };

    // Format phone number
    const formatPhone = (phone: string) => {
        if (phone.startsWith('+')) {
            return phone;
        }
        return `+${phone}`;
    };

    return (
        <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
            <StatusBar barStyle="dark-content" />

            {/* Header */}
            <View style={styles.header}>
                <TouchableOpacity
                    style={styles.backButton}
                    onPress={() => navigation.goBack()}
                >
                    <CIcon source={Iback} size={24} tintColor="#000" />
                </TouchableOpacity>
                <Text style={styles.headerTitle}>{getTaskType(task.task_code)}</Text>
                <TouchableOpacity style={styles.callButton}>
                    <CIcon source={Iphone} size={24} tintColor={COLORS.light.primary} />
                </TouchableOpacity>
            </View>

            <ResponsiveScrollView
                style={styles.scrollView}
                contentContainerStyle={styles.scrollViewContent}
                withPadding={false}
            >
                {/* Task Info */}
                <View style={styles.taskInfoContainer}>
                    <View style={styles.taskCodeRow}>
                        <Text style={styles.taskCode}>#{task.task_code}</Text>
                        <TouchableOpacity>
                            <CIcon source={Isetting} size={20} tintColor="#000" />
                        </TouchableOpacity>
                    </View>

                    <View style={styles.dateTimeRow}>
                        <CIcon source={Icalender} size={20} tintColor="#000" style={styles.icon} />
                        <Text style={styles.dateTime}>
                            {formatDateTime(task.scheduled_from)}
                        </Text>
                    </View>

                    <View style={styles.separator} />

                    {/* Customer Info */}
                    <View style={styles.customerInfoRow}>
                        <CIcon source={Iperson} size={20} tintColor="#000" style={styles.icon} />
                        <Text style={styles.customerName}>
                            {task.customer.name}
                        </Text>
                    </View>

                    <View style={styles.customerInfoRow}>
                        <CIcon source={Imap} size={20} tintColor={COLORS.light.primary} style={styles.icon} />
                        <Text style={styles.customerAddress}>
                            {task.customer.address}
                        </Text>
                    </View>

                    <View style={styles.customerInfoRow}>
                        <CIcon source={Iphone} size={20} tintColor="#000" style={styles.icon} />
                        <Text style={styles.customerPhone}>
                            {formatPhone(task.customer.phone)}
                        </Text>
                    </View>
                </View>

                {/* Product Information */}
                <View style={styles.sectionContainer}>
                    <Text style={styles.sectionTitle}>Product Information</Text>

                    <View style={styles.productsContainer}>
                        {mockProductItems.map((item) => (
                            <View key={item.id} style={styles.productItem}>
                                {!item.type && (
                                    <View style={styles.productRow}>
                                        <View style={styles.productImageContainer}>
                                            {item.image ? (
                                                <Image
                                                    source={item.image}
                                                    style={styles.productImage}
                                                />
                                            ) : (
                                                <View style={styles.productImagePlaceholder}>
                                                    <Text style={styles.productImagePlaceholderText}>
                                                        {item.name.substring(0, 2).toUpperCase()}
                                                    </Text>
                                                </View>
                                            )}
                                        </View>
                                        <View style={styles.productInfo}>
                                            <View style={styles.productHeader}>
                                                <Text style={styles.productName}>
                                                    Product: {item.name}
                                                </Text>
                                                <Text style={styles.productPrice}>
                                                    ${item.price.toFixed(2)}
                                                </Text>
                                            </View>
                                            <Text style={styles.productModel}>
                                                Model: {item.model}
                                            </Text>
                                            <Text style={styles.productQuantity}>
                                                Quantity: {item.quantity} {item.unit}
                                            </Text>
                                        </View>
                                        <TouchableOpacity style={styles.deleteButton}>
                                            <CIcon source={Iclose} size={20} tintColor={COLORS.light.primary} />
                                        </TouchableOpacity>
                                    </View>
                                )}

                                {item.type === 'service' && (
                                    <View style={styles.serviceRow}>
                                        <View style={styles.serviceInfo}>
                                            <View style={styles.productHeader}>
                                                <Text style={styles.productName}>
                                                    Service: {item.name}
                                                </Text>
                                                <Text style={styles.productPrice}>
                                                    ${item.price.toFixed(2)}
                                                </Text>
                                            </View>
                                            <Text style={styles.productName}>
                                                Product: {item.productName}
                                            </Text>
                                            <Text style={styles.productModel}>
                                                Model: {item.model}
                                            </Text>
                                        </View>
                                        <TouchableOpacity style={styles.deleteButton}>
                                            <CIcon source={Iclose} size={20} tintColor={COLORS.light.primary} />
                                        </TouchableOpacity>
                                    </View>
                                )}

                                {item !== mockProductItems[mockProductItems.length - 1] && (
                                    <View style={styles.productSeparator} />
                                )}
                            </View>
                        ))}
                    </View>
                </View>

                {/* Notes Section */}
                <View style={styles.sectionContainer}>
                    <View style={styles.tabContainer}>
                        <TouchableOpacity
                            style={[
                                styles.tab,
                                activeTab === 'internal' && styles.activeTab
                            ]}
                            onPress={() => setActiveTab('internal')}
                        >
                            <Text style={[
                                styles.tabText,
                                activeTab === 'internal' ? styles.activeTabText : styles.inactiveTabText
                            ]}>
                                Internal Notes
                            </Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={[
                                styles.tab,
                                activeTab === 'external' && styles.activeTab
                            ]}
                            onPress={() => setActiveTab('external')}
                        >
                            <Text style={[
                                styles.tabText,
                                activeTab === 'external' ? styles.activeTabText : styles.inactiveTabText
                            ]}>
                                External Notes
                            </Text>
                        </TouchableOpacity>
                    </View>

                    <View style={styles.notesContent}>
                        <Text style={styles.notesText}>
                            {activeTab === 'internal' ? mockNotes.internal : mockNotes.external}
                        </Text>
                    </View>
                </View>
            </ResponsiveScrollView>

            {/* Start Job Button */}
            <View style={[
                styles.bottomContainer,
                {
                    paddingBottom: Platform.OS === 'android' ?
                        Math.max(insets.bottom, INSETS.navigation.android) :
                        insets.bottom || SPACING.md
                }
            ]}>
                <TouchableOpacity style={styles.startButton}>
                    <Text style={styles.startButtonText}>Start Job</Text>
                </TouchableOpacity>
            </View>
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f5faff',
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: SPACING.md,
        paddingVertical: SPACING.sm,
        backgroundColor: '#fff',
        borderBottomWidth: 1,
        borderBottomColor: '#e0e0e0',
    },
    backButton: {
        paddingRight: SPACING.md,
    },
    headerTitle: {
        flex: 1,
        fontSize: 18,
        fontWeight: 'bold',
    },
    callButton: {
        padding: SPACING.xs,
    },
    scrollView: {
        flex: 1,
    },
    scrollViewContent: {
        flexGrow: 1,
    },
    taskInfoContainer: {
        backgroundColor: '#fff',
        padding: SPACING.md,
        marginBottom: SPACING.xs,
    },
    taskCodeRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: SPACING.md,
    },
    taskCode: {
        fontSize: 16,
        fontWeight: 'bold',
    },
    dateTimeRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: SPACING.md,
    },
    dateTime: {
        fontSize: 15,
    },
    separator: {
        height: 1,
        backgroundColor: '#e0e0e0',
        marginVertical: SPACING.md,
    },
    customerInfoRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: SPACING.sm,
    },
    icon: {
        marginRight: SPACING.sm,
        width: 20,
    },
    customerName: {
        fontSize: 16,
        fontWeight: '500',
    },
    customerAddress: {
        fontSize: 15,
        color: '#444',
        flex: 1,
    },
    customerPhone: {
        fontSize: 15,
    },
    sectionContainer: {
        backgroundColor: '#fff',
        padding: SPACING.md,
        marginBottom: SPACING.md,
    },
    sectionTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        marginBottom: SPACING.md,
    },
    productsContainer: {
        backgroundColor: '#fff',
        borderWidth: 1,
        borderColor: '#e0e0e0',
        borderRadius: BORDER_RADIUS.sm,
        overflow: 'hidden',
    },
    productItem: {
        paddingVertical: SPACING.sm,
    },
    productRow: {
        flexDirection: 'row',
        padding: SPACING.sm,
    },
    serviceRow: {
        flexDirection: 'row',
        padding: SPACING.sm,
        justifyContent: 'space-between',
    },
    productImageContainer: {
        width: 60,
        height: 60,
        borderRadius: BORDER_RADIUS.sm,
        overflow: 'hidden',
        marginRight: SPACING.sm,
    },
    productImage: {
        width: '100%',
        height: '100%',
        resizeMode: 'cover',
    },
    productImagePlaceholder: {
        width: '100%',
        height: '100%',
        backgroundColor: '#f0f0f0',
        borderRadius: BORDER_RADIUS.sm,
        justifyContent: 'center',
        alignItems: 'center',
    },
    productImagePlaceholderText: {
        fontSize: 14,
        fontWeight: 'bold',
        color: '#888',
    },
    productInfo: {
        flex: 1,
    },
    serviceInfo: {
        flex: 1,
    },
    productHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 2,
    },
    productName: {
        fontSize: 15,
        fontWeight: '500',
    },
    productPrice: {
        fontSize: 15,
        color: COLORS.light.primary,
        fontWeight: '500',
    },
    productModel: {
        fontSize: 14,
        color: '#666',
        marginVertical: 2,
    },
    productQuantity: {
        fontSize: 14,
        color: '#666',
    },
    deleteButton: {
        padding: SPACING.xs,
        alignSelf: 'center',
    },
    productSeparator: {
        height: 1,
        backgroundColor: '#e0e0e0',
        marginHorizontal: SPACING.md,
        marginTop: SPACING.sm,
    },
    tabContainer: {
        flexDirection: 'row',
        marginBottom: SPACING.md,
    },
    tab: {
        flex: 1,
        paddingVertical: SPACING.sm,
        alignItems: 'center',
        borderBottomWidth: 2,
        borderBottomColor: 'transparent',
    },
    activeTab: {
        borderBottomColor: COLORS.light.primary,
    },
    tabText: {
        fontSize: 15,
        fontWeight: '500',
    },
    activeTabText: {
        color: COLORS.light.primary,
    },
    inactiveTabText: {
        color: '#888',
    },
    notesContent: {
        paddingVertical: SPACING.sm,
    },
    notesText: {
        fontSize: 15,
        lineHeight: 22,
        color: '#444',
    },
    bottomContainer: {
        backgroundColor: '#fff',
        padding: SPACING.md,
        borderTopWidth: 1,
        borderTopColor: '#e0e0e0',
    },
    startButton: {
        backgroundColor: COLORS.light.primary,
        borderRadius: BORDER_RADIUS.md,
        padding: SPACING.md,
        alignItems: 'center',
    },
    startButtonText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: 'bold',
    },
});

export default TaskDetailScreen; 
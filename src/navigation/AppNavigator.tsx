import { ScreenConfig } from "@/types";
import { createDrawerNavigator, DrawerNavigationOptions } from "@react-navigation/drawer";
import CustomDrawer from "@/components/CustomDrawer";
import { useTheme } from "@react-navigation/native";
import { FONT_FAMILY, FONT_WEIGHT } from "@/constants/UI/themes";
import { Dimensions, StatusBar, ViewStyle } from "react-native";
import { useCallback, useEffect, useRef } from "react";
import { NAVIGATOR_TYPES, getScreenConfigsByType } from "@/constants/navigation";
import { BottomSheetProvider } from "@/hocs/BottomSheetContext";
import PermissionsHandler from "@/components/PermissionsHandler";
import { useDispatch } from "react-redux";
import { AppDispatch } from "@/redux/store";
import { getUserInfo } from "@/redux/slices/userSlice";


export function AppNavigator() {

    const { colors } = useTheme();
    const Drawer = createDrawerNavigator();
    const { width } = Dimensions.get('window');
    const isDrawerOpen = useRef(false);
    const dispatch = useDispatch<AppDispatch>();

    const screenList: ScreenConfig[] = getScreenConfigsByType(NAVIGATOR_TYPES.DRAWER);

    const renderScreenList = useCallback(() => {
        return screenList.map((screen, index) => (
            <Drawer.Screen
                key={index}
                name={screen.name}
                component={screen.component}
                options={screen.options as DrawerNavigationOptions}
            />
        ));
    }, [screenList]);

    const handlePermissionsGranted = useCallback(() => {
        dispatch(getUserInfo()).unwrap()
            .then((result) => {
                console.log('fetchUserInfo result', result);
            })
            .catch((error) => {
                console.log('fetchUserInfo error', error);
            });
    }, [dispatch]);

    return (
        <PermissionsHandler
            requiredPermissions={['notification', 'location']}
            optionalPermissions={['camera', 'storage']}
            autoRequestRequired={true}
            autoRequestOptional={false}
            onPermissionsGranted={handlePermissionsGranted}
        >
            <BottomSheetProvider>
                <Drawer.Navigator
                    initialRouteName={screenList[0].name}
                    drawerContent={(props) => <CustomDrawer {...props} />}
                    screenOptions={({ navigation }) => ({
                        headerStyle: {
                            backgroundColor: colors.background,
                        },
                        headerTintColor: colors.text,
                        headerTitleStyle: {
                            fontFamily: FONT_FAMILY.bold,
                            fontWeight: FONT_WEIGHT.semiBold,
                        },

                        drawerStyle: {
                            width: width,
                            backgroundColor: colors.background,
                        } as ViewStyle,
                        drawerType: 'front',
                        overlayColor: 'rgba(0,0,0,0.7)',
                        swipeEdgeWidth: 100,
                        drawerLabelStyle: {
                            fontFamily: FONT_FAMILY.regular,
                            marginLeft: -16,
                        },
                        drawerActiveTintColor: colors.primary,
                        drawerInactiveTintColor: colors.text,
                    })}
                    screenListeners={({ navigation }) => ({
                        state: (e) => {
                            const isOpen = navigation.getState().history.some(h => h.type === 'drawer');
                            isDrawerOpen.current = isOpen;
                            StatusBar.setHidden(isOpen);
                        }
                    })}
                >

                    {renderScreenList()}

                </Drawer.Navigator>
            </BottomSheetProvider>

        </PermissionsHandler>

    );
}
import { DEFAULT_USER_ROLE } from "@/constants/app";
import { DEFAULT_USER_NAME } from "@/constants/app"

import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import ProfileAvatar from "./ProfileAvatar";
import { FONT_SIZE, FONT_WEIGHT } from "@/constants/UI/themes";
import { BORDER_RADIUS } from "@/constants/UI/themes";
import { SPACING } from "@/constants/UI/themes";
import { COLORS } from "@/constants/UI/themes";
import { DEFAULT_AVATAR_URL } from "@/constants/app";
import CIcon from "./CIcon";
import { Ichange } from "@/constants/UI/icons";

export default function ProfileSection({ data, handleProfilePress }: { data: any, handleProfilePress?: () => void }) {
    return (
        <View style={styles.profileSection}>
            <ProfileAvatar
                source={{ uri: data?.profileImageUrl || DEFAULT_AVATAR_URL }}
                style={styles.avatar}
                name={data?.name || DEFAULT_USER_NAME}
            />
            <View style={styles.profileInfo}>
                <Text style={styles.profileName}>{data?.name || DEFAULT_USER_NAME}</Text>
                <Text style={styles.profileRole}>{data?.job_title || DEFAULT_USER_ROLE}</Text>
            </View>
            {handleProfilePress && <TouchableOpacity onPress={handleProfilePress} style={styles.settingIcon}>
                <CIcon source={Ichange} size={24} tintColor={COLORS.light.primary} />
            </TouchableOpacity>}
        </View>
    )
}


const styles = StyleSheet.create({
    profileSection: {
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: SPACING.lg,
        paddingHorizontal: SPACING.md,
    },
    avatar: {
        width: FONT_SIZE.size64,
        height: FONT_SIZE.size64,
        borderRadius: BORDER_RADIUS.round,
        borderWidth: 1,
        borderColor: COLORS.light.black,
    },
    profileInfo: {
        flex: 1,
        alignItems: 'center',
    },
    profileName: {
        fontSize: FONT_SIZE.xxl,
        fontWeight: FONT_WEIGHT.semiBold,
        color: COLORS.light.primary,
        marginTop: SPACING.sm,
    },
    profileRole: {
        fontSize: FONT_SIZE.size20,
        color: COLORS.light.textSecondary,
        marginTop: 2,
    },
    settingIcon: {
        position: 'absolute',
        top: SPACING.lg,
        right: SPACING.md,
    },
});
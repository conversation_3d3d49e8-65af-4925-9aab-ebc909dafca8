// src/components/ImagePicker/PhotoLibraryModal.tsx
import React, { useState, useEffect, useCallback } from 'react';
import {
    View,
    StyleSheet,
    TouchableOpacity,
    Image,
    FlatList,
    Modal,
    SafeAreaView,
    Alert,
    Platform
} from 'react-native';
import { CameraRoll } from '@react-native-camera-roll/camera-roll';
import { useTheme } from '@react-navigation/native';
import CText from '@/components/CText';
import { FONT_WEIGHT } from '@/constants/UI/themes';
import { usePermission } from '@/hooks/usePermission';

interface PhotoLibraryModalProps {
    visible: boolean;
    onClose: () => void;
    onSelect: (selectedImages: any[]) => void;
    multiple?: boolean;
    maxImages?: number;
}

interface GalleryImage {
    uri: string;
    selected: boolean;
}

const PhotoLibraryModal: React.FC<PhotoLibraryModalProps> = ({
    visible,
    onClose,
    onSelect,
    multiple = false,
    maxImages = 5,
}) => {
    const { colors } = useTheme() as any;
    const [galleryImages, setGalleryImages] = useState<GalleryImage[]>([]);

    // Permission hooks
    const {
        requestMediaPermissions,
        requestBothStoragePermissions,
        request: requestPhotoLibraryPermission
    } = usePermission('photoLibrary');

    // Load photos when modal becomes visible
    useEffect(() => {
        if (visible) {
            loadPhotos();
        }
    }, [visible]);

    const loadPhotos = async () => {
        let granted = false;

        // Check photo library permission
        if (Platform.OS === 'ios') {
            granted = await requestPhotoLibraryPermission();
        } else {
            if (Number(Platform.Version) >= 33) {
                granted = await requestMediaPermissions?.() || false;
            } else {
                granted = await requestBothStoragePermissions?.() || false;
            }
        }

        if (!granted) {
            Alert.alert(
                'Permission Required',
                'The app needs photo library access to select images',
                [{ text: 'OK' }]
            );
            onClose();
            return;
        }

        try {
            const photos = await CameraRoll.getPhotos({
                first: 50,
                assetType: 'Photos',
            });
            const formattedPhotos = photos.edges.map(edge => ({
                ...edge.node.image,
                selected: false,
            }));

            setGalleryImages(formattedPhotos);
        } catch (error) {
            console.error('Error loading photos:', error);
            Alert.alert('Error', 'Unable to load photos from library');
            onClose();
        }
    };

    const toggleImageSelection = useCallback((index: number) => {
        setGalleryImages(prev => {
            const updatedImages = [...prev];

            if (!multiple) {
                // If not selecting multiple images, deselect all other images
                updatedImages.forEach((img, i) => {
                    updatedImages[i].selected = i === index;
                });
            } else {
                // If selecting multiple images
                const selectedCount = updatedImages.filter(img => img.selected).length;

                // If image is already selected, deselect it
                if (updatedImages[index].selected) {
                    updatedImages[index].selected = false;
                }
                // If image is not selected and haven't reached the limit, select it
                else if (selectedCount < maxImages) {
                    updatedImages[index].selected = true;
                }
                // If reached the limit, show notification
                else {
                    Alert.alert('Notice', `You can only select up to ${maxImages} images`);
                }
            }

            return updatedImages;
        });
    }, [multiple, maxImages]);

    const confirmSelection = useCallback(() => {

        const selectedImages = galleryImages
            .filter(img => img.selected)
            .map(img => img);

        if (selectedImages.length === 0) {
            Alert.alert('Notice', 'Please select at least one image');
            return;
        }

        onSelect(selectedImages);
        onClose();
    }, [galleryImages, onSelect, onClose]);

    const renderGalleryItem = useCallback(({ item, index }: { item: GalleryImage; index: number }) => (
        <TouchableOpacity
            style={[
                styles.imageItem,
                item.selected && { borderColor: colors.primary, borderWidth: 2 }
            ]}
            onPress={() => toggleImageSelection(index)}
        >
            <Image source={{ uri: item.uri }} style={styles.thumbnail} />
            {item.selected && (
                <View style={[styles.selectedIndicator, { backgroundColor: colors.primary }]}>
                    <CText style={styles.selectedText}>✓</CText>
                </View>
            )}
        </TouchableOpacity>
    ), [colors.primary, toggleImageSelection]);

    return (
        <Modal
            visible={visible}
            animationType="slide"
            onRequestClose={onClose}
        >
            <SafeAreaView style={[styles.modalContainer, { backgroundColor: colors.background }]}>
                <View style={styles.header}>
                    <TouchableOpacity onPress={onClose}>
                        <CText style={[styles.headerButton, { color: colors.text }]}>Cancel</CText>
                    </TouchableOpacity>
                    <CText style={[styles.headerTitle, { color: colors.text }]}>
                        {multiple ? `Select up to ${maxImages} images` : 'Select image'}
                    </CText>
                    <TouchableOpacity onPress={confirmSelection}>
                        <CText style={[styles.headerButton, { color: colors.primary }]}>Done</CText>
                    </TouchableOpacity>
                </View>

                <FlatList
                    data={galleryImages}
                    renderItem={renderGalleryItem}
                    keyExtractor={(item, index) => index.toString()}
                    numColumns={3}
                    contentContainerStyle={styles.imageGrid}
                />
            </SafeAreaView>
        </Modal>
    );
};

const styles = StyleSheet.create({
    modalContainer: {
        flex: 1,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#e0e0e0',
    },
    headerButton: {
        fontSize: 16,
        fontWeight: FONT_WEIGHT.medium,
    },
    headerTitle: {
        fontSize: 16,
        fontWeight: FONT_WEIGHT.semiBold,
    },
    imageGrid: {
        padding: 4,
    },
    imageItem: {
        flex: 1,
        margin: 4,
        aspectRatio: 1,
        borderRadius: 8,
        overflow: 'hidden',
    },
    thumbnail: {
        width: '100%',
        height: '100%',
    },
    selectedIndicator: {
        position: 'absolute',
        top: 8,
        right: 8,
        width: 24,
        height: 24,
        borderRadius: 12,
        alignItems: 'center',
        justifyContent: 'center',
    },
    selectedText: {
        color: '#ffffff',
        fontWeight: FONT_WEIGHT.bold,
    },
});

export default PhotoLibraryModal;
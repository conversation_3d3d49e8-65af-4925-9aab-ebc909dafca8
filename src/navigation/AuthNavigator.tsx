import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { getScreenConfigsByType, NAVIGATOR_TYPES } from "@/constants/navigation";
import { ScreenConfig } from "@/types";
import { useTheme } from "@react-navigation/native";
import { FONT_FAMILY, FONT_WEIGHT } from "@/constants/UI/themes";
import { NativeStackNavigationOptions } from "@react-navigation/native-stack";
export function AuthNavigator() {
    const { colors } = useTheme();
    const Stack = createNativeStackNavigator();
    const screenList: ScreenConfig[] = getScreenConfigsByType(NAVIGATOR_TYPES.STACK);

    return (
        <Stack.Navigator
            initialRouteName={screenList[0].name}
            screenOptions={{
                headerStyle: {
                    backgroundColor: colors.background,
                },
                headerTintColor: colors.text,
                headerTitleStyle: {
                    fontFamily: FONT_FAMILY.bold,
                    fontWeight: FONT_WEIGHT.semiBold,
                },
                headerShown: false
            }}
        >
            {screenList.map((screen, index) => (
                <Stack.Screen
                    key={index}
                    name={screen.name}
                    component={screen.component}
                    options={screen.options as NativeStackNavigationOptions}
                />
            ))}
        </Stack.Navigator>
    );
}

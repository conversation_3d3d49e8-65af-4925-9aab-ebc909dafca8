import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import CInput from './CInput';
import CIcon from './CIcon';
import { Ieye, IeyeOff, Isetting } from '@/constants/UI/icons';
import { COLORS, SPACING, BORDER_RADIUS } from '@/constants/UI/themes';

interface PasswordInputProps {
    label: string;
    value: string;
    onChangeText: (text: string) => void;
    placeholder: string;
    error?: string;
    required?: boolean;
}

const PasswordInput = ({
    label,
    value,
    onChangeText,
    placeholder,
    error,
    required = true
}: PasswordInputProps) => {
    const [showPassword, setShowPassword] = useState(false);

    return (
        <View style={styles.inputGroup}>
            <View style={styles.labelContainer}>
                <Text style={styles.inputLabel}>{label}</Text>
                {required && <Text style={styles.requiredMark}>*</Text>}
            </View>
            <View style={styles.passwordContainer}>
                <CInput
                    value={value}
                    onChangeText={onChangeText}
                    style={styles.input}
                    contentStyle={styles.inputContent}
                    secureTextEntry={!showPassword}
                    placeholder={placeholder}
                />
                <TouchableOpacity
                    style={styles.eyeIcon}
                    onPress={() => setShowPassword(!showPassword)}
                >
                    <CIcon source={showPassword ? Ieye : IeyeOff} size={20} tintColor={COLORS.light.textSecondary} />
                </TouchableOpacity>
            </View>
            {error && <Text style={styles.errorText}>{error}</Text>}
        </View>
    );
};

const styles = StyleSheet.create({
    inputGroup: {
        marginBottom: SPACING.md,
    },
    labelContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: SPACING.xs,
    },
    inputLabel: {
        fontSize: 16,
    },
    requiredMark: {
        color: COLORS.light.error,
        marginLeft: 4,
        fontSize: 16,
    },
    passwordContainer: {
        position: 'relative',
    },
    input: {
        height: 45,
        borderWidth: 1,
        borderColor: '#e0e0e0',
        borderRadius: BORDER_RADIUS.sm,
    },
    inputContent: {
        paddingHorizontal: SPACING.md,
        paddingRight: 50
    },
    eyeIcon: {
        position: 'absolute',
        right: 15,
        top: 12,
        zIndex: 1,
    },
    errorText: {
        color: COLORS.light.error,
        fontSize: 14,
    },
});

export default PasswordInput; 
import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useTheme } from '@react-navigation/native';
import CText from './CText';
import { FONT_SIZE } from '@/constants/UI/themes';

interface CDropdownProps {
    label: string;
    items: { label: string; value: string }[];
    value: string;
    onValueChange: (value: string) => void;
}

const CDropdown: React.FC<CDropdownProps> = ({ label, items, value, onValueChange }) => {
    const { colors } = useTheme();

    return (
        <View style={styles.container}>
            <CText size="md" style={[styles.label, { color: colors.text }]}>
                {label}
            </CText>
            <View style={[styles.pickerContainer, { borderColor: colors.border }]}>

            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        marginBottom: 16,
    },
    label: {
        marginBottom: 4,
    },
    pickerContainer: {
        borderWidth: 1,
        borderRadius: 8,
    },
    picker: {
        height: 44,
        fontSize: FONT_SIZE.lg,
    },
});

export default CDropdown;
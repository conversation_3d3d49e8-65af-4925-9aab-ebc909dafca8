import { useTranslation } from 'react-i18next';
import { useCallback } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

const LANGUAGE_KEY = '@app_language';

export const useLanguage = () => {
    const { i18n } = useTranslation();

    const changeLanguage = useCallback(async (language: string) => {
        try {
            await AsyncStorage.setItem(LANGUAGE_KEY, language);
            await i18n.changeLanguage(language);
        } catch (error) {
            console.error('Error changing language:', error);
        }
    }, [i18n]);

    const getCurrentLanguage = useCallback(() => {
        return i18n.language;
    }, [i18n]);

    const initializeLanguage = useCallback(async () => {
        try {
            const savedLanguage = await AsyncStorage.getItem(LANGUAGE_KEY);
            if (savedLanguage) {
                await i18n.changeLanguage(savedLanguage);
            }
        } catch (error) {
            console.error('Error initializing language:', error);
        }
    }, [i18n]);

    return {
        changeLanguage,
        getCurrentLanguage,
        initializeLanguage,
    };
}; 
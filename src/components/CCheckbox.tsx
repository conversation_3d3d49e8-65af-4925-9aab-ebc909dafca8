import React from 'react';
import { TouchableOpacity, View, StyleSheet } from 'react-native';
import { useTheme } from '@react-navigation/native';
import CText from './CText';
import CIcon from './CIcon';
import { BORDER_RADIUS, FONT_SIZE } from '@/constants/UI/themes';
import { Icheck } from '@/constants/UI/icons';

interface CCheckboxProps {
    label: string;
    value: boolean;
    onChange: (value: boolean) => void;
}

const CCheckbox: React.FC<CCheckboxProps> = ({ label, value, onChange }) => {
    const { colors } = useTheme();

    return (
        <TouchableOpacity
            style={styles.container}
            onPress={() => onChange(!value)}
        >
            <View
                style={[
                    styles.checkbox,
                    {
                        borderColor: colors.primary,
                        backgroundColor: colors.background,
                    },
                ]}
            >
                {value && <CIcon source={Icheck} size={FONT_SIZE.xxl} tintColor={colors.primary} />}
            </View>
            <CText style={[styles.label, { color: colors.text }]}>{label}</CText>
        </TouchableOpacity>
    );
};

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    checkbox: {
        width: FONT_SIZE.xxl,
        height: FONT_SIZE.xxl,
        borderWidth: 1,
        borderRadius: BORDER_RADIUS.xsm,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 8,
    },
    label: {
        fontSize: FONT_SIZE.md,
    },
});

export default CCheckbox;
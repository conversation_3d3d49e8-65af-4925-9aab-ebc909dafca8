export const capitalize = (str: string) => {
    if (!str) return '';
    return str.charAt(0).toUpperCase() + str.slice(1);
};

export const removeAllWhitespace = (str: string) => {
    if (!str) return '';
    return str.replace(/\s/g, '');
};

export const trim = (str: string) => {
    if (!str) return '';
    return str.trim();
};

export const normalizeWhitespace = (str: string) => {
    if (!str) return '';
    return str.replace(/\s+/g, ' ').trim();
};

export const getInitials = (name?: string) => {
    if (!name) return '';
    return name
        .split(' ')
        .map(word => word.charAt(0))
        .join('')
        .toUpperCase()
        .slice(0, 2);
};

import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { Calendar as RNCalendar } from 'react-native-calendars';
import { format } from 'date-fns';
import { SPACING, COLORS } from '@/constants/UI/themes';
import { DateHeader } from './DateHeader';

interface CalendarV2Props {
    selectedDate: Date;
    onDateSelect: (date: Date) => void;
    onClose?: () => void;
}

export const Calendar2: React.FC<CalendarV2Props> = ({
    selectedDate,
    onDateSelect,
    onClose
}) => {
    const handleDayPress = (day: any) => {
        const newDate = new Date(day.timestamp);
        onDateSelect(newDate);
        if (onClose) {
            onClose();
        }
    };

    return (
        <View style={styles.container}>
            <RNCalendar
                current={format(selectedDate, 'yyyy-MM-dd')}
                onDayPress={handleDayPress}
                markedDates={{
                    [format(selectedDate, 'yyyy-MM-dd')]: {
                        selected: true,
                        selectedColor: COLORS.light.primary,
                    },
                }}
                theme={{
                    todayTextColor: COLORS.light.primary,
                    selectedDayBackgroundColor: COLORS.light.primary,
                    arrowColor: COLORS.light.primary,
                }}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        backgroundColor: COLORS.light.white,
        borderRadius: 8,
        padding: SPACING.md,
        shadowColor: COLORS.light.text,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
}); 
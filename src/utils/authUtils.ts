import * as Keychain from 'react-native-keychain';
import { jwtDecode } from 'jwt-decode';
import { LoginResponse, TokenPayloadDecoded } from '@/types';
import { AUTH_TOKEN_KEY, REFRESH_TOKEN_KEY } from '@/constants/api';



interface JWTPayload {
    exp: number;
    jti: string;
    iss: string;
    aud: string;
    sub: string;
    nbf: number;
    iat: number;
}

export const isTokenExpired = (token: string): boolean => {
    try {
        const decoded = jwtDecode<JWTPayload>(token);
        const currentTime = Date.now() / 1000; // Convert to seconds
        return decoded.exp < currentTime;
    } catch (error) {
        console.error('Error decoding token:', error);
        //if token is not valid, return true
        return true;
    }
};


// Save token to Keychain securely
export const saveToken = async (access_token: keyof LoginResponse, refresh_token?: keyof LoginResponse): Promise<boolean> => {
    try {
        // For v10.0.0, use simpler options
        await Keychain.setGenericPassword(
            AUTH_TOKEN_KEY,
            access_token,
            {
                service: AUTH_TOKEN_KEY,
                accessible: Keychain.ACCESSIBLE.WHEN_UNLOCKED,
                // accessControl: Keychain.ACCESS_CONTROL.BIOMETRY_ANY
            }
        );

        if (refresh_token) {
            await Keychain.setGenericPassword(
                REFRESH_TOKEN_KEY,
                refresh_token,
                {
                    service: REFRESH_TOKEN_KEY,
                    accessible: Keychain.ACCESSIBLE.WHEN_UNLOCKED,
                    // accessControl: Keychain.ACCESS_CONTROL.BIOMETRY_ANY
                }
            );
        }
        return true;
    } catch (error) {
        console.error('Error saving token to Keychain:', error);
        return false;
    }
};

// Get token from Keychain
export const getToken = async (): Promise<string | null> => {
    try {
        // Don't pass authentication options to getGenericPassword to avoid biometric prompts
        const credentials = await Keychain.getGenericPassword({ service: AUTH_TOKEN_KEY });

        if (credentials) {
            const token = credentials.password;

            if (isTokenExpired(token)) {
                await clearTokens();
                return null;
            }

            return token;
        }
        return null;
    } catch (error) {
        console.error('Error retrieving token from Keychain:', error);

        // Try to handle specific errors
        if (error instanceof Error &&
            (error.message.includes('User not authenticated') ||
                error.message.includes('resetGenericPasswordForOptions'))) {
            // Token requires authentication but user is not authenticated
            // Return null to let the app handle re-authentication
            return null;
        }

        return null;
    }
};

// Get refresh token from Keychain
export const getRefreshToken = async (): Promise<string | null> => {
    try {
        // Don't pass authentication options to getGenericPassword to avoid biometric prompts
        const credentials = await Keychain.getGenericPassword({ service: REFRESH_TOKEN_KEY });
        if (credentials) {
            return credentials.password;
        }
        return null;
    } catch (error) {
        console.error('Error retrieving refresh token from Keychain:', error);

        // Try to handle specific errors
        if (error instanceof Error &&
            (error.message.includes('User not authenticated') ||
                error.message.includes('getGenericPasswordForOptions'))) {
            // Token requires authentication but user is not authenticated
            // Return null to let the app handle re-authentication
            return null;
        }

        return null;
    }
};

// Clear tokens from Keychain (for logout)
export const clearTokens = async (): Promise<boolean> => {
    try {
        // Try simpler approach first without options
        try {
            await Keychain.resetGenericPassword({ service: AUTH_TOKEN_KEY });
        } catch (error) {
            console.warn('Error clearing AUTH_TOKEN, trying again with default options:', error);
            // If that fails, use fallback approach
            await Keychain.resetGenericPassword();
        }

        try {
            await Keychain.resetGenericPassword({ service: REFRESH_TOKEN_KEY });
        } catch (error) {
            console.warn('Error clearing REFRESH_TOKEN, service option not working:', error);
            // No need to retry, already cleared with default above
        }

        return true;
    } catch (error) {
        console.error('Error clearing tokens from Keychain:', error);
        return false;
    }
};

// Check if token is valid (not expired)
export const isTokenValid = (token: string): boolean => {
    try {
        const decodedToken: any = jwtDecode(token);
        const currentTime = Date.now() / 1000;

        // Check if token has expired
        if (decodedToken.exp && decodedToken.exp < currentTime) {
            return false;
        }

        return true;
    } catch (error) {
        console.error('Error validating token:', error);
        return false;
    }
};

// Get user information from token
export const getUserFromToken = (token: string): Partial<TokenPayloadDecoded> | null => {
    try {
        const decodedToken: any = jwtDecode(token);
        return {
            user_id: decodedToken.user_id,
            session_id: decodedToken.session_id,
            url: decodedToken.url,
            iss: decodedToken.iss,
            aud: decodedToken.aud,
            exp: decodedToken.exp,
        };
    } catch (error) {
        console.error('Error decoding token:', error);
        return null;
    }
};

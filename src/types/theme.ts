
// Font related types
export type FontSizeKeys = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl' | 'xxxl' | 'x4l' | 'x5l' | 'x6l';
export type FontWeightKeys = 'light' | 'regular' | 'medium' | 'semiBold' | 'bold' | 'extraBold';
export type FontWeightType = '300' | '400' | '500' | '600' | '700' | '800';
export type FontFamilyKeys = 'thin' | 'thinItalic' | 'light' | 'lightItalic' | 'regular' | 'italic' | 'bold' | 'boldItalic' | 'black' | 'blackItalic';

// Layout related types
export type SpacingKeys = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl' | 'size30';
export type BorderRadiusKeys =
    | 'xs'  // 4
    | 'xsm' // 6
    | 'sm'  // 8
    | 'md'  // 12
    | 'lg'  // 16
    | 'xl'  // 24
    | 'round';
export type PaddingKeys = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl';

// Theme related types
export type ThemeOptions = 'light' | 'dark';

// Spacing configuration
export interface SpacingConfig {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
    xxl: number;
}

// Border radius configuration
export interface BorderRadiusConfig {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
    round: number;
}

// Font size configuration
export interface FontSizeConfig {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
    xxl: number;
    xxxl: number;
    x4l: number;
    x5l: number;
    x6l: number;
}

export interface ThemeColors {
    primary: string;
    background: string;
    card: string;
    text: string;
    border: string;
    notification: string;
    surface: string;
    onPrimary: string;
    placeholder: string;
    error: string;
    secondary: string;
    backgroundGradient: string[];
    white: string;
    black: string;
    textSecondary: string;
    success: string;
    warning: string;
    info: string;
    disabled: string;
    blurPopup: string;
    linearGradient: string[];
    price: string;
}

export interface Colors {
    light: ThemeColors;
    dark: ThemeColors;
}

export interface FontStyle {
    fontFamily: string;
    fontWeight: FontWeightType;
    fontSize: number;
    lineHeight?: number;
}

export interface Typography {
    fontSizes: FontSizeConfig;
    fontWeights: Record<FontWeightKeys, FontWeightType>;
    fontFamilies: Record<FontFamilyKeys, string>;
}

export interface Layout {
    spacing: SpacingConfig;
    borderRadius: BorderRadiusConfig;
    padding: Record<PaddingKeys, number>;
}

export interface ThemeSizes {
    borderRadius: {
        small: number;
        medium: number;
        large: number;
    };
    inputHeight: number;
    buttonHeight: number;
    font: {
        small: number;
        medium: number;
        large: number;
    };
}

export interface ThemeSpacing {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
}

export interface ThemeFonts {
    bodySmall: {
        fontSize: number;
        fontFamily: string;
    };
    bodyMedium: {
        fontSize: number;
        fontFamily: string;
    };
    bodyLarge: {
        fontSize: number;
        fontFamily: string;
    };
    titleMedium: {
        fontSize: number;
        fontFamily: string;
    };
}

export interface AppTheme {
    dark: boolean;
    colors: ThemeColors;
    sizes: ThemeSizes;
    spacing: ThemeSpacing;
    fonts: ThemeFonts;
}

// Shadow related types
export interface ShadowStyle {
    elevation: number;
    shadowColor: string;
    shadowOpacity: number;
    shadowRadius: number;
    shadowOffset: { width: number; height: number };
}
export type ShadowKeys = 'small' | 'medium' | 'large';

export interface ShadowConfig {
    small: ShadowStyle;
    medium: ShadowStyle;
    large: ShadowStyle;
}

// Animation related types
export interface AnimationDuration {
    short: number;
    medium: number;
    long: number;
}

export interface AnimationEasing {
    standard: string;
    decelerate: string;
    accelerate: string;
}

export interface Animations {
    duration: AnimationDuration;
    easing: AnimationEasing;
}

// Other UI related types
export type OpacityKeys = 'disabled' | 'pressed' | 'overlay';
export interface OpacityConfig {
    disabled: number;
    pressed: number;
    overlay: number;
}

export type ZIndexKeys = 'base' | 'elevated' | 'dropdown' | 'modal' | 'loading' | 'tooltip' | 'toast';
export interface ZIndexConfig {
    base: number;
    elevated: number;
    dropdown: number;
    modal: number;
    loading: number;
    tooltip: number;
    toast: number;
}

// Platform specific types
export type InsetKeys = 'keyboard' | 'navigation' | 'input' | 'content';
export interface InsetConfig {
    keyboard: number;
    navigation: number;
    input: number;
    content: number;
}

export type LayoutKeys = 'containerPadding' | 'contentSpacing';
export interface LayoutConfig {
    containerPadding: number;
    contentSpacing: number;
}

// Complete theme configuration
export interface ThemeConfig {
    colors: Colors;
    typography: Typography;
    layout: Layout;
    shadows: ShadowConfig;
    animations: Animations;
    opacity: OpacityConfig;
    zIndex: ZIndexConfig;
    insets: InsetConfig;
    layoutConfig: LayoutConfig;
    sizes: ThemeSizes;
    spacing: ThemeSpacing;
    fonts: ThemeFonts;
} 
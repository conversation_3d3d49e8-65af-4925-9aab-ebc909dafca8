

const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');
const {
    wrapWithReanimatedMetroConfig,
} = require('react-native-reanimated/metro-config');

const config = {
    // Your existing Metro configuration options
};


/**
 * Solution 1
 */
const mergedConfig = mergeConfig(getDefaultConfig(__dirname), config);
module.exports = wrapWithReanimatedMetroConfig(mergedConfig);

/**
 * Solution 2
 */
// module.exports = wrapWithReanimatedMetroConfig(
//     mergeConfig(getDefaultConfig(__dirname), config)
// );
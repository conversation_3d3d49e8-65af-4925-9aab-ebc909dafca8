# Project configuration
  System:
    OS: macOS 15.3.2
    
  Binaries:
    Node:
      version: 22.9.0
      path: ~/.nvm/versions/node/v22.9.0/bin/node
    Yarn: Not Found
    npm:
      version: 10.8.3
      path: ~/.nvm/versions/node/v22.9.0/bin/npm
    Watchman:
      version: 2024.10.07.00
      path: /opt/homebrew/bin/watchman
  Managers:
    CocoaPods:
      version: 1.15.2
      path: /Users/<USER>/.rvm/rubies/ruby-3.1.0/bin/pod
  SDKs:
    iOS SDK:
      Platforms:
        - DriverKit 24.2
        - iOS 18.2
        - macOS 15.2
        - tvOS 18.2
        - visionOS 2.2
        - watchOS 11.2
    Android SDK: Not Found
  IDEs:
    Android Studio: 2024.2 AI-242.21829.142.2421.12409432
    Xcode:
      version: 16.2/16C5032a
      path: /usr/bin/xcodebuild
  Languages:
    Java:
      version: 17.0.12
      path: /usr/bin/javac
    Ruby:
      version: 3.1.0
      path: /Users/<USER>/.rvm/rubies/ruby-3.1.0/bin/ruby
  npmPackages:
    "@react-native-community/cli":
      installed: 15.0.1
      wanted: 15.0.1
    react:
      installed: 19.0.0
      wanted: 19.0.0
    react-native:
      installed: 0.78.1
      wanted: 0.78.1
    react-native-macos: Not Found
  npmGlobalPackages:
    "*react-native*": Not Found
  Android:
    hermesEnabled: true
    newArchEnabled: true
  iOS:
    hermesEnabled: true
    newArchEnabled: true

# React Native Config:
  Search for the path "/Users/<USER>/Documents/avb-mobile-app" and replace it with the local build machine's app path
# Update 
  In the .xcode.env.local file, update the path to the Node version if necessary:
  export NODE_BINARY=xxxxx/versions/node/v22.9.0/bin/node
# Building
Init: 
  - npm install
  - cd ios && pod install && cd ..
  Android:
  - npm run android:dev
  - npm run android:stg
  - npm run android:pro

  IOS:
  - Open ARC.xcworkspace and choose a build scheme.


import {
    PanResponder,
} from 'react-native';
import { NavigationProp, useNavigation, useTheme } from '@react-navigation/native';
import React, { useState, useRef, useEffect } from 'react';
import {
    Text,
    StyleSheet,
    View,
    Image,
    ImageSourcePropType,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import withResponsiveLayout from '@/hocs/withResponsiveLayout';
import { DrawerParamList } from '@/types';
import { TaskList } from '@/components/TaskList';
import { SPACING, COLORS, ICON_SIZE } from '@/constants/UI/themes';
import { Calendar2 } from '@/components/Calendar_v2';
import { DateHeader } from '@/components/DateHeader';
import { WeekDays } from '@/components/WeekDays';
import { Iarrow, Isetting, Imap } from '@/constants/UI/icons';
import { mockTasks } from '@/constants/mock/tasks';
import { TaskListData } from '@/interfaces/task';
import CIcon from '@/components/CIcon';

function DashboardScreen(props: any) {
    const navigation = useNavigation<NavigationProp<DrawerParamList>>();
    const [refreshing, setRefreshing] = useState(false);
    const [selectedDate, setSelectedDate] = useState(new Date());
    const [showCalendar, setShowCalendar] = useState(false);
    const { colors } = useTheme();
    const { t } = useTranslation();
    const [taskData, setTaskData] = useState<TaskListData>({
        ...mockTasks,
        records: mockTasks.records.slice(0, 12), // Initially show only 12 items
    });

    useEffect(() => {
        navigation.setOptions({
            headerTitle: () => (
                <DateHeader
                    date={selectedDate}
                    onPress={() => setShowCalendar(!showCalendar)}
                    textStyle={{ color: colors.text }}
                    iconColor={colors.text}
                    isCalendarVisible={showCalendar}
                />
            ),
        });
    }, [navigation, selectedDate, showCalendar, colors]);

    const onRefresh = () => {
        setRefreshing(true);

        // Reset to initial 12 items
        setTimeout(() => {
            setTaskData({
                ...mockTasks,
                records: mockTasks.records.slice(0, 12),
                has_more: true, // Reset has_more flag
            });
            setRefreshing(false);
        }, 2000);
    };

    const handleDateSelect = (date: Date) => {
        setSelectedDate(date);
        // Here you could filter tasks based on the selected date
        // This would typically involve an API call in a real app
    };

    const handleLoadMore = () => {
        // Simulate loading more items
        if (taskData.records.length < mockTasks.records.length) {
            // Get the remaining items, up to 3 more
            const currentCount = taskData.records.length;
            const newItems = mockTasks.records.slice(
                currentCount,
                Math.min(currentCount + 3, mockTasks.records.length)
            );

            // Update state with new items
            setTaskData({
                ...taskData,
                records: [...taskData.records, ...newItems],
                has_more: currentCount + 3 < mockTasks.records.length,
            });
        }
    };

    return (
        <View style={styles.container}>
            {/* Conditionally render Calendar popup */}
            {showCalendar && (
                <View style={styles.calendarPopup}>
                    <Calendar2
                        selectedDate={selectedDate}
                        onDateSelect={(date: Date) => {
                            handleDateSelect(date);
                            setShowCalendar(false);
                        }}
                        onClose={() => setShowCalendar(false)}
                    />
                </View>
            )}

            {/* Header Section with WeekDays */}
            {/* <View style={styles.headerSection}>
                <WeekDays
                    selectedDate={selectedDate}
                    onDayPress={handleDateSelect}
                />
                <View style={styles.jobsContainer}>
                    <Text style={styles.jobsText}>{t('dashboard.tasks', { count: taskData.records.length })}</Text>
                    <CIcon source={Imap} size={30} tintColor={COLORS.light.primary} />
                </View>
            </View> */}

            {/* Body Section */}
            {/* <TaskList
                data={taskData}
                onRefresh={onRefresh}
                refreshing={refreshing}
                onLoadMore={handleLoadMore}
            /> */}
        </View>
    );
}

export default withResponsiveLayout(DashboardScreen);

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: COLORS.light.background,
    },
    calendarPopup: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        zIndex: 1000,
        elevation: 5,
        backgroundColor: COLORS.light.white,
        shadowColor: COLORS.light.text,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
    },
    headerSection: {
        borderBottomWidth: 1,
        borderBottomColor: COLORS.light.border,
        marginBottom: SPACING.xs,
    },
    jobsContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: SPACING.md,
        paddingVertical: SPACING.xs,
    },
    jobsText: {
        fontSize: 16,
        fontWeight: '500',
    },
    iconImage: {
        width: 20,
        height: 20,
        tintColor: COLORS.light.primary,
    },
});
import { combineReducers } from "@reduxjs/toolkit";
import authReducer from '../slices/authSlice';
import themeReducer from '../slices/themeSlice';
import userReducer from '../slices/userSlice';
import loadingReducer from '../slices/loadingSlice';

const rootReducer = combineReducers({
    auth: authReducer,
    theme: themeReducer,
    user: userReducer,
    loading: loadingReducer
});

export default rootReducer;
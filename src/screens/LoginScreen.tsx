import React, { useState, useEffect, useRef } from 'react';
import {
    View,
    TouchableOpacity,
    Image,
    ActivityIndicator,
    TextInput,
    ImageProps,
} from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { AppDispatch, RootState } from '../redux/store';
import { loginUser } from '../redux/slices/authSlice';
import { Controller } from 'react-hook-form';
import withForm from '../hocs/withForm';
import LinearGradient from 'react-native-linear-gradient';
import { NavigationProp, useNavigation, useTheme } from '@react-navigation/native';
import { Text } from 'react-native-gesture-handler';
import { loginSchema } from '@/constants/validationSchema';
import CCheckbox from '@/components/CCheckbox';
import { Ieye, IeyeOff, Ilogo } from '@/constants/UI/icons';
import { AppTheme, LoginData, StackParamList } from '@/types';
import CInput from '@/components/CInput';
import { COLORS, GRADIENT_VALUE } from '@/constants/UI/themes';
import { useTranslation } from 'react-i18next';
import { createAuthStyles } from '@/styles/authStyles';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { SCREEN_NAMES } from '@/constants/navigation';
import { STORAGE_KEYS } from '@/constants/auth';
import { useToast } from '@/hocs/toast';
import { removeItem, storeString } from '@/utils/storageUtils';
import CIcon from '@/components/CIcon';
import { useLoading } from '@/hocs/LoadingContext';


const LoginScreen = (props: any) => {
    const theme = useTheme() as unknown as AppTheme;
    const { colors } = theme;
    const { t } = useTranslation();
    const dispatch = useDispatch<AppDispatch>();
    const navigation = useNavigation<NavigationProp<StackParamList>>();
    const { loading } = useSelector(
        (state: RootState) => state.auth
    );
    const { handleSubmit, control, formState: { errors }, setValue } = props;
    const [remember, setRemember] = useState(false);
    const [isSecure, setIsSecure] = useState(true);
    const styles = createAuthStyles(theme);
    const { showToast } = useToast();
    const { show, hide, withLoading } = useLoading();
    const urlInputRef = React.useRef<TextInput>(null);

    // Load saved credentials on component mount
    useEffect(() => {
        const loadSavedCredentials = async () => {
            try {
                const rememberedValue = await AsyncStorage.getItem(STORAGE_KEYS.REMEMBER_ME);
                if (rememberedValue === 'true') {
                    setRemember(true);
                    const savedEmail = await AsyncStorage.getItem(STORAGE_KEYS.USER_EMAIL);
                    const savedUrl = await AsyncStorage.getItem(STORAGE_KEYS.USER_URL);

                    if (savedEmail) {
                        setValue('email', savedEmail);
                    }

                    if (savedUrl) {
                        setValue('url', savedUrl);
                    }
                }
            } catch (error) {
                console.error('Error loading saved credentials:', error);
            }
        };

        loadSavedCredentials();
    }, [setValue]);


    const onSubmit = async (data: LoginData) => {
        try {

            show();
            dispatch(loginUser({
                email: data.email,
                password: data.password,
                url: data.url
            })).unwrap()
                .then((result) => {
                    showToast(result.message, 'success');
                    hide();
                })
                .catch((error) => {
                    showToast(error as string, 'error');
                    hide();
                });

            // Save credentials if remember me is checked
            if (remember) {
                storeString(STORAGE_KEYS.REMEMBER_ME, 'true');
                storeString(STORAGE_KEYS.USER_EMAIL, data.email);
                if (data.url) {
                    storeString(STORAGE_KEYS.USER_URL, data.url);
                }
            } else {
                // Clear saved credentials if remember me is unchecked
                removeItem(STORAGE_KEYS.REMEMBER_ME);
                removeItem(STORAGE_KEYS.USER_EMAIL);
                removeItem(STORAGE_KEYS.USER_URL);
            }
        } catch (err) {
            console.error('Login dispatch error:', err);
        }
    };

    const onToggleRemember = () => {
        setRemember(!remember);
    };

    const goToForgetPasswordScreen = () => {
        navigation.navigate(SCREEN_NAMES.FORGOT_PASSWORD as keyof StackParamList);
    };

    const renderForm = () => {
        const fields = ["url", "email", "password"];
        return (
            <View style={styles.formContainer}>

                <View>
                    {fields.map((_field) => {
                        return (
                            (
                                <View style={styles.fieldContainer} key={_field}>
                                    <View style={styles.labelContainer}>
                                        <Text style={styles.label}>{t(`fields.${_field}.label`)}</Text>
                                        <Text style={styles.required}>*</Text>
                                    </View>
                                    <Controller
                                        control={control}
                                        name={_field}
                                        render={({ field: { onChange, value, onBlur } }) => (
                                            <View>
                                                {_field == 'url' && (
                                                    <View style={styles.urlContainer}>
                                                        <Text
                                                            style={styles.urlPrefix}
                                                            onPress={() => urlInputRef.current?.focus()}
                                                        >
                                                            https://
                                                        </Text>
                                                        <TextInput
                                                            ref={urlInputRef}
                                                            placeholder={t(`fields.url.placeholder`)}
                                                            value={value?.replace('https://', '')}
                                                            onChangeText={(text) => onChange(`https://${text}`)}
                                                            onBlur={() => {
                                                                if (!value || value === 'https://') {
                                                                    onChange('');
                                                                }
                                                                if (onBlur) {
                                                                    onBlur();
                                                                }
                                                            }}
                                                            style={styles.urlInput}
                                                            placeholderTextColor={colors.placeholder}
                                                        />
                                                    </View>
                                                )}
                                                {_field == 'email' && (
                                                    <CInput
                                                        value={value}
                                                        onChangeText={onChange}
                                                        onBlur={onBlur}
                                                        style={styles.input}
                                                        placeholder={t('fields.email.placeholder')}
                                                        keyboardType="email-address"
                                                        autoCapitalize="none"
                                                    />
                                                )}
                                                {_field == 'password' && (
                                                    <View style={styles.passwordContainer}>
                                                        <CInput
                                                            value={value}
                                                            onChangeText={onChange}
                                                            onBlur={onBlur}
                                                            style={styles.input}
                                                            placeholder={t('fields.password.placeholder')}
                                                            secureTextEntry={isSecure}
                                                        />
                                                        <TouchableOpacity
                                                            style={styles.eyeIcon}
                                                            onPress={() => setIsSecure(!isSecure)}
                                                        >
                                                            <CIcon
                                                                source={isSecure ? Ieye : IeyeOff}
                                                                size={20}
                                                                tintColor={COLORS.light.textSecondary}
                                                            />
                                                        </TouchableOpacity>
                                                    </View>
                                                )}
                                            </View>
                                        )}
                                    />
                                    {errors[_field] && (
                                        <Text style={styles.error}>{errors[_field]?.message as string}</Text>
                                    )}
                                </View>
                            )
                        )
                    })}

                    <View style={styles.checkboxContainer}>
                        <CCheckbox
                            label={t('auth.rememberMe')}
                            value={remember}
                            onChange={onToggleRemember}
                        />
                    </View>
                </View>



                <View style={{ flexDirection: 'column', justifyContent: 'space-between', }}>
                    <TouchableOpacity
                        style={styles.actionButton}
                        onPress={!loading ? handleSubmit(onSubmit) : undefined}
                    >
                        <Text style={styles.actionButtonText}>{t('auth.signin')}</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                        style={styles.linkButton}
                        onPress={goToForgetPasswordScreen}
                    >
                        <Text style={styles.linkButtonText}>{t('auth.forgotPassword')}?</Text>
                    </TouchableOpacity>
                </View>

            </View>
        );
    };

    return (
        <LinearGradient
            colors={colors.backgroundGradient}
            locations={GRADIENT_VALUE}
            style={styles.container}
        >
            <View style={styles.logoContainer}>
                <Image source={Ilogo as ImageProps} style={styles.logo} />
            </View>
            {renderForm()}
        </LinearGradient>
    );
};

const EnhancedLoginScreen = withForm<FormData>(LoginScreen);

export default () => (
    <EnhancedLoginScreen
        defaultValues={{
            url: '',
            email: '',
            password: '',
        }}
        schema={loginSchema}
        onSubmit={() => { }}
    />
);
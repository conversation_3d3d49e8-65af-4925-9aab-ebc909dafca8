import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { User, ApiResponse } from '@/types';
import * as authUtils from '@/utils/authUtils';
import i18n from '@/i18n/i18n';
import authService from '@/services/authService';

export interface AuthState {
    isAuthenticated: boolean;
    loading: boolean;
    error: string | null;
}

const initialState: AuthState = {
    isAuthenticated: false,
    loading: false,
    error: null,
};

export const loginUser = createAsyncThunk(
    'auth/loginUser',
    async ({ email, password, url }: { email: string; password: string; url: string }, { dispatch, rejectWithValue, }) => {
        try {
            const response: ApiResponse = await authService.login({ email, password, url });

            if (!response.success || !response.data.access_token) {
                return rejectWithValue(response.message);
            }

            try {
                await authUtils.saveToken(response.data.access_token, response.data.refresh_token);
            } catch (keychainError) {
                console.error('Error saving token to Keychain:', keychainError);
            }

            return response;
        } catch (error: any) {
            const errorMessage = error && typeof error === 'object' && error.message
                ? error.message
                : i18n.t('errors.somethingWentWrong');
            return rejectWithValue(errorMessage);
        }
    }
);

export const refreshUserToken = createAsyncThunk(
    'auth/refreshUserToken',
    async (_, { rejectWithValue }) => {
        try {
            const refreshToken = await authUtils.getRefreshToken();

            if (!refreshToken) {
                throw new Error('Refresh token not found');
            }

            const response = await authService.refreshToken(refreshToken);

            if (!response.success || !response.data.access_token) {
                throw new Error(response.message);
            }

            // Try to save the refreshed token, but continue even if it fails
            try {
                await authUtils.saveToken(response.data.access_token, response.data.refresh_token);
            } catch (keychainError) {
                console.error('Error saving refreshed token to Keychain:', keychainError);
            }

        } catch (error: any) {
            return rejectWithValue(error.message || 'Làm mới token thất bại');
        }
    }
);

export const logoutUser = createAsyncThunk(
    'auth/logoutUser',
    async (_, { rejectWithValue }) => {
        try {
            const response = await authService.logout();

            if (!response.success) {
                return rejectWithValue(response.message);
            }

            // Try to clear tokens but don't fail if clearing fails
            try {
                await authUtils.clearTokens();
            } catch (keychainError) {
                console.error('Error clearing tokens from Keychain:', keychainError);
            }
            return response;

        } catch (error: any) {
            const errorMessage = error && typeof error === 'object' && error.message
                ? error.message
                : i18n.t('errors.somethingWentWrong');
            return rejectWithValue(errorMessage);
        }
    }
);


export const checkAuth = createAsyncThunk(
    'auth/checkAuth',
    async (_, { dispatch }) => {
        try {
            const token = await authUtils.getToken();

            if (!token) {
                return { isAuthenticated: false };
            }

            // check if token is expired
            if (authUtils.isTokenExpired(token)) {
                // refresh token
                const refreshToken = await authUtils.getRefreshToken();
                if (refreshToken) {
                    try {
                        const response = await authService.refreshToken(refreshToken);
                        if (response.success && response.data.access_token) {
                            // save new token
                            await authUtils.saveToken(
                                response.data.access_token,
                                response.data.refresh_token
                            );
                            return { isAuthenticated: true };
                        }
                    } catch (error) {
                        console.error('Token refresh failed:', error);
                        await authUtils.clearTokens();
                        return { isAuthenticated: false };
                    }
                }
            }

            // Token is valid
            return { isAuthenticated: true };
        } catch (error) {
            console.error('Check auth error:', error);
            return { isAuthenticated: false };
        }
    }
);



const authSlice = createSlice({
    name: 'auth',
    initialState,
    reducers: {
        setLoading(state, action: PayloadAction<boolean>) {
            state.loading = action.payload;
        },
        setError(state, action: PayloadAction<string | null>) {
            state.error = action.payload;
        },
        resetAuth(state) {
            state.isAuthenticated = false;
            state.error = null;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(loginUser.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(loginUser.fulfilled, (state, action) => {
                state.loading = false;
                state.isAuthenticated = true;
            })
            .addCase(loginUser.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string;
            })

            .addCase(refreshUserToken.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(refreshUserToken.fulfilled, (state, action) => {
                state.loading = false;
                if (action.payload) {
                    state.isAuthenticated = true;
                }
            })
            .addCase(refreshUserToken.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string;
                state.isAuthenticated = false;
            })

            .addCase(logoutUser.pending, (state) => {
                state.loading = true;
            })
            .addCase(logoutUser.fulfilled, (state) => {
                state.loading = false;
                state.isAuthenticated = false;
                state.error = null;
            })
            .addCase(logoutUser.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string;
            })

            .addCase(checkAuth.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(checkAuth.fulfilled, (state, action) => {
                state.loading = false;
                if (action.payload) {
                    state.isAuthenticated = action.payload.isAuthenticated;
                } else {
                    state.isAuthenticated = false;
                }
            })
            .addCase(checkAuth.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string;
                state.isAuthenticated = false;
            })
    }
});

export const { setLoading, setError, resetAuth } = authSlice.actions;
export default authSlice.reducer;
import React from 'react';
import { StyleSheet, ViewStyle, StyleProp, Keyboard, TouchableWithoutFeedback } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import ResponsiveContainer from '../components/ResponsiveContainer';
import { BASE_STYLE } from '@/constants/UI/responsiveStyles';

interface WithResponsiveLayoutProps {
    containerStyle?: StyleProp<ViewStyle>;
    fluid?: boolean;
    ignoreBottomInset?: boolean;
    keyboardAvoidingEnabled?: boolean;
}

const withResponsiveLayout = <P extends object>(
    WrappedComponent: React.ComponentType<P>
) => {
    const WithResponsiveLayoutComponent: React.FC<P & WithResponsiveLayoutProps> = ({
        containerStyle,
        fluid,
        ignoreBottomInset,
        keyboardAvoidingEnabled = true,
        ...props
    }) => {

        const renderContent = () => (
            <ResponsiveContainer
                style={containerStyle}
                fluid={fluid}
                ignoreBottomInset={ignoreBottomInset}
            >
                <WrappedComponent {...(props as P)} />
            </ResponsiveContainer>
        );

        if (!keyboardAvoidingEnabled) {
            return (
                <SafeAreaView
                    style={[styles.safeArea]}
                    edges={['top', 'left', 'right']}
                >
                    {renderContent()}
                </SafeAreaView>
            );
        }

        return (
            <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
                <SafeAreaView
                    style={[styles.safeArea]}
                    edges={['left', 'right']}
                >
                    {renderContent()}
                </SafeAreaView>
            </TouchableWithoutFeedback>
        );
    };

    WithResponsiveLayoutComponent.displayName = `WithResponsiveLayout(${WrappedComponent.displayName || WrappedComponent.name || 'Component'
        })`;

    return WithResponsiveLayoutComponent;
};

const styles = StyleSheet.create({
    safeArea: {
        flex: BASE_STYLE.flex.flex,
        backgroundColor: 'transparent',
    },
});

export default withResponsiveLayout;
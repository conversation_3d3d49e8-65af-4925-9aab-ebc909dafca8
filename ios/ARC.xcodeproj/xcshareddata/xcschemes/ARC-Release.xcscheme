<?xml version="1.0" encoding="UTF-8"?>
<Scheme
   LastUpgradeVersion = "1620"
   version = "1.7">
   <BuildAction
      parallelizeBuildables = "YES"
      buildImplicitDependencies = "YES"
      buildArchitectures = "Automatic">
      <PreActions>
         <ExecutionAction
            ActionType = "Xcode.IDEStandardExecutionActionsCore.ExecutionActionType.ShellScriptAction">
            <ActionContent
               title = "Run Script"
               scriptText = "echo &quot;&#x1f501; Generating xcconfig for CONFIGURATION = $CONFIGURATION&quot;&#10;&#10;if [ &quot;$CONFIGURATION&quot; = &quot;Debug&quot; ]; then&#10;  export ENVFILE=&quot;/Users/<USER>/Documents/avb-mobile-app/.env&quot;&#10;elif [ &quot;$CONFIGURATION&quot; = &quot;Staging&quot; ]; then&#10;  export ENVFILE=&quot;/Users/<USER>/Documents/avb-mobile-app/.env.staging&quot;&#10;elif [ &quot;$CONFIGURATION&quot; = &quot;Release&quot; ]; then&#10;  export ENVFILE=&quot;/Users/<USER>/Documents/avb-mobile-app/.env.production&quot;&#10;fi&#10;&#10;echo &quot;&#x1f4e6; Using ENVFILE=$ENVFILE&quot;&#10;&#10;ruby &quot;/Users/<USER>/Documents/avb-mobile-app/node_modules/react-native-config/ios/ReactNativeConfig/BuildXCConfig.rb&quot; \&#10;  &quot;/Users/<USER>/Documents/avb-mobile-app&quot; \&#10;  &quot;/Users/<USER>/Documents/avb-mobile-app/ios/tmp.xcconfig&quot;&#10;">
               <EnvironmentBuildable>
                  <BuildableReference
                     BuildableIdentifier = "primary"
                     BlueprintIdentifier = "13B07F861A680F5B00A75B9A"
                     BuildableName = "ARC (Dev).app"
                     BlueprintName = "ARC"
                     ReferencedContainer = "container:ARC.xcodeproj">
                  </BuildableReference>
               </EnvironmentBuildable>
            </ActionContent>
         </ExecutionAction>
      </PreActions>
      <BuildActionEntries>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "YES"
            buildForProfiling = "YES"
            buildForArchiving = "YES"
            buildForAnalyzing = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "13B07F861A680F5B00A75B9A"
               BuildableName = "ARC (Dev).app"
               BlueprintName = "ARC"
               ReferencedContainer = "container:ARC.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
      </BuildActionEntries>
   </BuildAction>
   <TestAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      shouldUseLaunchSchemeArgsEnv = "YES"
      shouldAutocreateTestPlan = "YES">
   </TestAction>
   <LaunchAction
      buildConfiguration = "Release"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      launchStyle = "0"
      useCustomWorkingDirectory = "NO"
      ignoresPersistentStateOnLaunch = "NO"
      debugDocumentVersioning = "YES"
      debugServiceExtension = "internal"
      allowLocationSimulation = "YES">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "13B07F861A680F5B00A75B9A"
            BuildableName = "ARC (Dev).app"
            BlueprintName = "ARC"
            ReferencedContainer = "container:ARC.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
   </LaunchAction>
   <ProfileAction
      buildConfiguration = "Release"
      shouldUseLaunchSchemeArgsEnv = "YES"
      savedToolIdentifier = ""
      useCustomWorkingDirectory = "NO"
      debugDocumentVersioning = "YES">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "13B07F861A680F5B00A75B9A"
            BuildableName = "ARC (Dev).app"
            BlueprintName = "ARC"
            ReferencedContainer = "container:ARC.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
   </ProfileAction>
   <AnalyzeAction
      buildConfiguration = "Debug">
   </AnalyzeAction>
   <ArchiveAction
      buildConfiguration = "Release"
      revealArchiveInOrganizer = "YES">
   </ArchiveAction>
</Scheme>

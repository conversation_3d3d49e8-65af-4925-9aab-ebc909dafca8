// enhancers.ts
import { Reducer } from '@reduxjs/toolkit';

export const monitorReducerEnhancer = (createStore: any) => (reducer: Reducer, initialState: any, enhancer: any) => {
    const monitoredReducer: Reducer = (state, action) => {
        const start = Date.now();
        const newState = reducer(state, action);
        const duration = Date.now() - start;
        console.log(`Reducer took ${duration}ms for action ${action.type}`);
        return newState;
    };

    return createStore(monitoredReducer, initialState, enhancer);
};
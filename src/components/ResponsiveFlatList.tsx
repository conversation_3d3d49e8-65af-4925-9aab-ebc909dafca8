import React from 'react';
import {
    FlatList,
    FlatListProps,
    StyleSheet,
    ViewStyle,
    StyleProp,
    useWindowDimensions,
    Platform,
    View,
    ListRenderItem,
    RefreshControl,
    DimensionValue,
} from 'react-native';
import { PADDING, SPACING, INSETS } from '@/constants/UI/themes';
import { getBreakpoint, isLandscape } from '@/constants/UI/responsive';
import { DeviceType } from '@/types';
import { BASE_STYLE } from '@/constants/UI/responsiveStyles';

interface ResponsiveFlatListProps<T> extends Omit<FlatListProps<T>, 'numColumns'> {
    containerStyle?: StyleProp<ViewStyle>;
    contentContainerStyle?: StyleProp<ViewStyle>;
    withPadding?: boolean;
    withSpacing?: boolean;
    responsive?: boolean;
    onRefresh?: () => void;
    refreshing?: boolean;
    numColumns?: number | { mobile: number; tablet: number; desktop: number };
    itemContainerStyle?: StyleProp<ViewStyle>;
}

function ResponsiveFlatList<T>({
    data,
    renderItem: originalRenderItem,
    containerStyle,
    contentContainerStyle,
    withPadding = false,
    withSpacing = true,
    responsive = true,
    onRefresh,
    refreshing = false,
    numColumns = responsive ? { mobile: 1, tablet: 2, desktop: 3 } : 1,
    itemContainerStyle,
    ...props
}: ResponsiveFlatListProps<T>) {
    const { width, height } = useWindowDimensions();
    const breakpoint: DeviceType = getBreakpoint(width);
    const landscape = isLandscape(width, height);
    const isMobile = breakpoint === 'mobile' && !landscape;
    const isTablet = breakpoint === 'tablet' || (breakpoint === 'mobile' && landscape);

    const getResponsiveStyle = (): ViewStyle => {
        if (!responsive) return {};

        return {
            flexDirection: 'row',
            flexWrap: 'wrap',
            gap: withSpacing ? SPACING.md : 0,
        };
    };

    const getColumnCount = (): number => {
        if (typeof numColumns === 'number') return numColumns;

        if (isMobile) return numColumns.mobile;
        if (isTablet) return numColumns.tablet;
        return numColumns.desktop;
    };

    const columnCount = getColumnCount();

    const renderItem: ListRenderItem<T> = (info) => {
        if (!originalRenderItem) return null;

        const itemWidth = `${100 / columnCount}%` as DimensionValue;

        const originalElement = originalRenderItem(info);

        if (!originalElement) return null;

        return (
            <View
                style={[
                    styles.itemContainer,
                    {
                        // alignSelf: 'center',
                        width: itemWidth,
                        // paddingHorizontal: withSpacing ? SPACING.md / 2 : 0,
                        // paddingVertical: withSpacing ? SPACING.md / 2 : 0,
                    } as ViewStyle,
                    itemContainerStyle,
                ]}
            >
                {originalElement}
            </View>
        );
    };

    return (
        <FlatList
            style={[styles.container, containerStyle]}
            contentContainerStyle={[
                styles.contentContainer,
                withPadding && styles.withPadding,
                getResponsiveStyle(),
                contentContainerStyle,
            ]}
            data={data}
            renderItem={renderItem}
            showsVerticalScrollIndicator={false}
            bounces={true}
            overScrollMode="always"
            keyboardShouldPersistTaps="handled"
            keyboardDismissMode="on-drag"
            refreshControl={
                onRefresh ? (
                    <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
                ) : undefined
            }
            {...props}
        />
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    contentContainer: {
        flexGrow: 1,
        width: '100%',
        paddingBottom: Platform.OS === 'android' ? INSETS.navigation.android : 0,

    },
    withPadding: {
        padding: PADDING.md,
    },
    itemContainer: {
        flex: 1,
    },
});

export default ResponsiveFlatList; 
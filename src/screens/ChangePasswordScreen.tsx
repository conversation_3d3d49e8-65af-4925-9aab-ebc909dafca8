import React from 'react';
import { StyleSheet, View, Text, TouchableOpacity, Keyboard } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useFocusEffect, useNavigation, useTheme } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { ParamListBase } from '@react-navigation/native';
import { SPACING, COLORS, BORDER_RADIUS, GRADIENT_VALUE } from '@/constants/UI/themes';
import PasswordInput from '@/components/PasswordInput';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '@/redux/store';
import { useToast } from '@/hocs/toast';
import { changePassword } from '@/redux/slices/userSlice';
import i18n from '@/i18n/i18n';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { Controller, useForm } from 'react-hook-form';
import { AppTheme, ChangePasswordFormData } from '@/types';
import { changePasswordSchema } from '@/constants/validationSchema';
import { useLoading } from '@/hocs/LoadingContext';
import LinearGradient from 'react-native-linear-gradient';
import { adjustSpacing } from '@/constants/UI/responsive';
import { HeaderBackButton } from '@react-navigation/elements';
import { SCREEN_NAMES } from '@/constants/navigation';

type ChangePasswordScreenNavigationProp = NativeStackNavigationProp<ParamListBase>;


const ChangePasswordScreen = () => {
    const navigation = useNavigation<ChangePasswordScreenNavigationProp>();
    const dispatch = useDispatch<AppDispatch>();
    const { showToast } = useToast();
    const { show, hide, withLoading } = useLoading();
    const { control, handleSubmit, formState: { errors }, reset } = useForm<ChangePasswordFormData>({
        resolver: yupResolver(changePasswordSchema),
        defaultValues: {
            newPassword: '',
            currentPassword: '',
            confirmPassword: ''
        }
    });
    const { colors } = useTheme() as unknown as AppTheme;


    useFocusEffect(
        React.useCallback(() => {
            navigation.setOptions({
                headerLeft: () => (
                    <HeaderBackButton
                        onPress={() => navigation.navigate(SCREEN_NAMES.PROFILE)}
                        tintColor="#000"
                    />
                ),
                // swipeEnabled: false
            });
        }, [navigation])
    );

    const onSubmit = async (data: ChangePasswordFormData) => {
        Keyboard.dismiss();
        show();
        try {
            const response = await dispatch(changePassword({
                currentPassword: data.currentPassword,
                newPassword: data.newPassword,
                confirmPassword: data.confirmPassword
            })).unwrap();

            if (response.success) {
                showToast(i18n.t(response.data.message ? response.data.message : 'auth.passwordChanged'), 'success');
                reset();
                setTimeout(() => {
                    navigation.goBack();
                }, 2000);
            }
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : i18n.t('errors.somethingWentWrong');
            showToast(errorMessage, 'error');
        } finally {
            hide();
        }
    };

    return (
        <SafeAreaView style={styles.container} edges={['left', 'right']}>
            <LinearGradient
                colors={colors.linearGradient}
                locations={GRADIENT_VALUE}
                style={styles.container}
            >

                <View style={{ flexGrow: 1, justifyContent: 'space-between', paddingHorizontal: SPACING.md }}>

                    <View style={styles.formContainer}>
                        <Controller
                            control={control}
                            name="currentPassword"
                            render={({ field: { onChange, value } }: { field: { onChange: (text: string) => void; value: string } }) => (
                                <PasswordInput
                                    label={i18n.t('fields.currentPassword.label')}
                                    value={value}
                                    onChangeText={onChange}
                                    placeholder={i18n.t('fields.currentPassword.placeholder')}
                                    error={errors.currentPassword?.message}
                                />
                            )}
                        />

                        <Controller
                            control={control}
                            name="newPassword"
                            render={({ field: { onChange, value } }: { field: { onChange: (text: string) => void; value: string } }) => (
                                <PasswordInput
                                    label={i18n.t('fields.newPassword.label')}
                                    value={value}
                                    onChangeText={onChange}
                                    placeholder={i18n.t('fields.newPassword.placeholder')}
                                    error={errors.newPassword?.message}
                                />
                            )}
                        />

                        <Controller
                            control={control}
                            name="confirmPassword"
                            render={({ field: { onChange, value } }: { field: { onChange: (text: string) => void; value: string } }) => (
                                <PasswordInput
                                    label={i18n.t('fields.confirmPassword.label')}
                                    value={value}
                                    onChangeText={onChange}
                                    placeholder={i18n.t('fields.confirmPassword.placeholder')}
                                    error={errors.confirmPassword?.message}
                                />
                            )}
                        />
                    </View>

                    <TouchableOpacity
                        style={[styles.updateButton, { marginBottom: adjustSpacing(50), }]}
                        onPress={handleSubmit(onSubmit)}
                    >
                        <Text style={styles.updateButtonText}>
                            {i18n.t('common.update')}
                        </Text>
                    </TouchableOpacity>

                </View>
            </LinearGradient>

        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,

    },
    formContainer: {
        // padding: SPACING.md,
        marginTop: SPACING.md,
    },
    updateButton: {
        // backgroundColor: COLORS.light.primary,
        borderWidth: 1,
        borderRadius: BORDER_RADIUS.md,
        padding: SPACING.md,
        alignItems: 'center',
        // marginTop: SPACING.md,
        borderColor: COLORS.light.primary,
        marginVertical: SPACING.md,
        width: '100%',
    },
    updateButtonText: {
        color: COLORS.light.primary,
        fontSize: 16,
        fontWeight: 'bold',
    },
});

export default ChangePasswordScreen; 
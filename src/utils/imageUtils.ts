import { Platform, Image } from 'react-native';
import { CameraRoll } from '@react-native-camera-roll/camera-roll';
import { NativeModules } from 'react-native';

/**
 * Resize và chuyển ảnh sang base64 để gửi API
 * Utility này chỉ dùng Native API, không phụ thuộc thư viện bên ngoài
 * Lưu ý: Để có đầy đủ chức năng, bạn có thể cân nhắc các thư viện như:
 * - react-native-image-resizer
 * - react-native-image-manipulator
 * - react-native-compressor
 */

/**
 * Kiểu dữ liệu cho ảnh đã resize
 */
export interface ImageProcessResult {
    uri: string;       // Đường dẫn ảnh
    width: number;     // Chiều rộng
    height: number;    // Chiều cao
    base64?: string;   // Dữ liệu base64 (nếu có)
    size?: number;     // Kích thước file (bytes)
}

/**
 * L<PERSON>y thông tin kích thước của ảnh
 */
export const getImageInfo = (uri: string): Promise<{ width: number; height: number }> => {
    return new Promise((resolve, reject) => {
        Image.getSize(
            uri,
            (width, height) => {
                resolve({ width, height });
            },
            (error) => {
                console.error('Lỗi khi lấy kích thước ảnh:', error);
                reject(error);
            }
        );
    });
};

/**
 * Tính toán tỉ lệ khung hình để giữ nguyên tỉ lệ ảnh
 */
export const calculateAspectRatio = (
    originalWidth: number,
    originalHeight: number,
    maxWidth: number = 800,
    maxHeight: number = 800
): { width: number; height: number } => {
    let newWidth = originalWidth;
    let newHeight = originalHeight;

    // Nếu ảnh lớn hơn kích thước tối đa, resize
    if (originalWidth > maxWidth || originalHeight > maxHeight) {
        const widthRatio = maxWidth / originalWidth;
        const heightRatio = maxHeight / originalHeight;

        // Sử dụng tỉ lệ nhỏ hơn để đảm bảo ảnh vừa với cả hai giới hạn
        const ratio = Math.min(widthRatio, heightRatio);

        newWidth = Math.floor(originalWidth * ratio);
        newHeight = Math.floor(originalHeight * ratio);
    }

    return { width: newWidth, height: newHeight };
};

/**
 * Convert URI thành base64 - Phương pháp thuần React Native
 * 
 * Lưu ý: Phương pháp này CHỈ hoạt động với một số URI dạng file:// và một số URI khác
 * Không hoạt động với hầu hết content:// URI trên Android
 * 
 * @param uri URI của ảnh
 * @returns Promise với chuỗi base64 hoặc null nếu không thành công
 */
export const uriToBase64 = async (uri: string): Promise<string | null> => {
    // Xử lý URI để đảm bảo định dạng đúng
    const formattedUri = uri.startsWith('file://') ? uri :
        (Platform.OS === 'ios' && !uri.startsWith('file:') ? `file://${uri}` : uri);

    try {
        // Tạo XMLHttpRequest để đọc dữ liệu
        return await new Promise<string | null>((resolve, reject) => {
            const xhr = new XMLHttpRequest();
            xhr.onload = function () {
                if (xhr.status === 200) {
                    // Đọc dữ liệu dưới dạng ArrayBuffer
                    const reader = new FileReader();
                    reader.onload = function () {
                        const result = reader.result as string;
                        // Extract base64 data without the prefix (e.g., "data:image/jpeg;base64,")
                        const base64 = result.split(',')[1];
                        resolve(base64);
                    };
                    reader.readAsDataURL(new Blob([xhr.response]));
                } else {
                    reject(new Error(`Không thể đọc URI: ${xhr.status}`));
                }
            };
            xhr.onerror = function () {
                reject(new Error('Lỗi khi đọc file'));
            };
            xhr.open('GET', formattedUri);
            xhr.responseType = 'blob';
            xhr.send();
        });
    } catch (error) {
        console.error('Lỗi chuyển đổi URI sang base64:', error);
        return null;
    }
};

/**
 * Tính kích thước của chuỗi base64 (bytes)
 */
export const getBase64Size = (base64String: string): number => {
    // Base64 encoding tăng kích thước lên khoảng 33%
    return Math.floor(base64String.length * 0.75);
};

/**
 * Hàm chính: Xử lý ảnh để gửi lên API với kích thước tối ưu
 * 
 * Chiến lược: 
 * 1. Lấy thông tin kích thước ảnh
 * 2. Tính toán kích thước mới với tỉ lệ phù hợp
 * 3. Cố gắng chuyển đổi thành base64
 * 4. Nếu kích thước vẫn lớn, giảm dần kích thước
 * 
 * Lưu ý: Có thể không thành công với tất cả loại URI do hạn chế của React Native
 */
export const optimizeImageForUpload = async (
    imageUri: string,
    maxSizeBytes: number = 1024 * 1024, // Mặc định 1MB
    initialQuality: number = 0.8,       // Chất lượng ban đầu (0.0 - 1.0)
): Promise<ImageProcessResult | null> => {
    try {
        // 1. Lấy thông tin ảnh
        const { width, height } = await getImageInfo(imageUri);

        // 2. Tính toán kích thước ban đầu - giảm ngay nếu ảnh quá lớn
        let targetWidth = width;
        let targetHeight = height;

        // Nếu ảnh quá lớn (>2MP), giảm kích thước ngay
        const pixelCount = width * height;
        if (pixelCount > 2000000) { // 2 megapixels
            const scaleFactor = Math.sqrt(2000000 / pixelCount);
            targetWidth = Math.floor(width * scaleFactor);
            targetHeight = Math.floor(height * scaleFactor);
        }

        // 3. Thử chuyển đổi sang base64
        const base64 = await uriToBase64(imageUri);

        // 4. Nếu không lấy được base64, trả về thông tin ảnh cơ bản
        if (!base64) {
            console.log("Không thể lấy base64 từ URI này - một số URI không được hỗ trợ bởi React Native");
            return {
                uri: imageUri,
                width: targetWidth,
                height: targetHeight,
            };
        }

        // 5. Kiểm tra kích thước
        const size = getBase64Size(base64);

        // 6. Nếu kích thước đã phù hợp, trả về kết quả
        if (size <= maxSizeBytes) {
            return {
                uri: imageUri,
                width: targetWidth,
                height: targetHeight,
                base64,
                size
            };
        }

        // 7. Nếu vẫn lớn, cần lưu ý rằng không thể resize với React Native thuần
        console.log(`Ảnh vẫn lớn (${(size / 1024 / 1024).toFixed(2)}MB), cần thư viện native để resize`);

        // 8. Giải pháp thay thế: Trả về URI và thông báo
        return {
            uri: imageUri,
            width: targetWidth,
            height: targetHeight,
            base64,
            size
        };
    } catch (error) {
        console.error('Lỗi xử lý ảnh:', error);
        return null;
    }
};

/**
 * Đề xuất: Để triển khai đầy đủ, bạn cần bổ sung Native Module
 * 
 * Android: Viết một Native Module sử dụng BitmapFactory và Bitmap
 * iOS: Viết một Native Module sử dụng UIImageJPEGRepresentation
 * 
 * Ví dụ code Native Module cho Android:
 * 
 * @ReactMethod
 * public void resizeImage(String uri, int maxWidth, int maxHeight, float quality, Promise promise) {
 *   try {
 *     Bitmap sourceBitmap = MediaStore.Images.Media.getBitmap(reactContext.getContentResolver(), Uri.parse(uri));
 *     
 *     int width = sourceBitmap.getWidth();
 *     int height = sourceBitmap.getHeight();
 *     
 *     float aspectRatio = (float) width / height;
 *     
 *     if (width > maxWidth || height > maxHeight) {
 *       if (width > height) {
 *         width = maxWidth;
 *         height = (int) (width / aspectRatio);
 *       } else {
 *         height = maxHeight;
 *         width = (int) (height * aspectRatio);
 *       }
 *     }
 *     
 *     Bitmap resizedBitmap = Bitmap.createScaledBitmap(sourceBitmap, width, height, true);
 *     
 *     ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
 *     resizedBitmap.compress(Bitmap.CompressFormat.JPEG, (int)(quality * 100), byteArrayOutputStream);
 *     
 *     byte[] byteArray = byteArrayOutputStream.toByteArray();
 *     String base64 = Base64.encodeToString(byteArray, Base64.DEFAULT);
 *     
 *     WritableMap result = Arguments.createMap();
 *     result.putString("base64", base64);
 *     result.putInt("width", width);
 *     result.putInt("height", height);
 *     result.putInt("size", byteArray.length);
 *     
 *     promise.resolve(result);
 *   } catch (Exception e) {
 *     promise.reject("ERROR", e.getMessage());
 *   }
 * }
 */ 
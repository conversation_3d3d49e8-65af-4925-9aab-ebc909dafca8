import { Alert, Linking, Platform } from 'react-native';
import {
    check,
    request,
    PERMISSIONS,
    RESULTS,
    openSettings,
    checkMultiple,
    requestMultiple,
    Permission,
    checkNotifications,
    requestNotifications,
} from 'react-native-permissions';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Permission types supported by this utility
export type PermissionType =
    | 'camera'
    | 'location'
    | 'locationAlways'
    | 'notification'
    | 'storage'
    | 'microphone';

// Map permission types to actual permission keys from react-native-permissions
const PERMISSION_MAP: Record<PermissionType, Record<'ios' | 'android', Permission>> = {
    camera: {
        ios: PERMISSIONS.IOS.CAMERA,
        android: PERMISSIONS.ANDROID.CAMERA,
    },
    location: {
        ios: PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
        android: PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
    },
    locationAlways: {
        ios: PERMISSIONS.IOS.LOCATION_ALWAYS,
        android: PERMISSIONS.ANDROID.ACCESS_BACKGROUND_LOCATION,
    },
    notification: {
        ios: PERMISSIONS.IOS.CAMERA,
        android: 'android.permission.POST_NOTIFICATIONS' as Permission,
    },
    storage: {
        ios: PERMISSIONS.IOS.PHOTO_LIBRARY,
        android: PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE,
    },
    microphone: {
        ios: PERMISSIONS.IOS.MICROPHONE,
        android: PERMISSIONS.ANDROID.RECORD_AUDIO,
    },
};

// Keys for AsyncStorage
const PERMISSION_REQUESTED_KEY = 'permission_requested_';

/**
 * Check if a permission has been requested before
 * @param permissionType The type of permission to check
 * @returns True if the permission has been requested before
 */
export const hasRequestedPermission = async (permissionType: PermissionType): Promise<boolean> => {
    try {
        const value = await AsyncStorage.getItem(`${PERMISSION_REQUESTED_KEY}${permissionType}`);
        return value === 'true';
    } catch (error) {
        console.error('Error checking if permission was requested before:', error);
        return false;
    }
};

/**
 * Mark a permission as requested
 * @param permissionType The type of permission to mark
 */
export const markPermissionRequested = async (permissionType: PermissionType): Promise<void> => {
    try {
        await AsyncStorage.setItem(`${PERMISSION_REQUESTED_KEY}${permissionType}`, 'true');
    } catch (error) {
        console.error('Error marking permission as requested:', error);
    }
};

/**
 * Get the appropriate permission for the current platform
 * @param permissionType The type of permission
 * @returns The platform-specific permission
 */
export const getPermission = (permissionType: PermissionType): Permission => {
    const platform = Platform.OS as 'ios' | 'android';
    return PERMISSION_MAP[permissionType][platform];
};

/**
 * Kiểm tra quyền thông báo đặc biệt cho iOS
 * @returns True nếu quyền được cấp
 */
export const checkNotificationPermission = async (): Promise<boolean> => {
    if (Platform.OS === 'ios') {
        const { status } = await checkNotifications();
        return status === RESULTS.GRANTED;
    } else {
        return checkPermission('notification');
    }
};

/**
 * Yêu cầu quyền thông báo đặc biệt cho iOS
 * @returns True nếu quyền được cấp
 */
export const requestNotificationPermission = async (): Promise<boolean> => {
    if (Platform.OS === 'ios') {
        const { status } = await requestNotifications(['alert', 'sound', 'badge']);
        await markPermissionRequested('notification');
        return status === RESULTS.GRANTED;
    } else {
        return requestPermission('notification');
    }
};

/**
 * Check if a permission is granted
 * @param permissionType The type of permission to check
 * @returns True if the permission is granted
 */
export const checkPermission = async (permissionType: PermissionType): Promise<boolean> => {
    try {
        if (permissionType === 'notification' && Platform.OS === 'ios') {
            return checkNotificationPermission();
        }

        const permission = getPermission(permissionType);
        const result = await check(permission);
        return result === RESULTS.GRANTED;
    } catch (error) {
        console.error(`Error checking ${permissionType} permission:`, error);
        return false;
    }
};

/**
 * Check multiple permissions at once
 * @param permissionTypes Array of permission types to check
 * @returns Object with permission types as keys and boolean values indicating if granted
 */
export const checkPermissions = async (permissionTypes: PermissionType[]): Promise<Record<PermissionType, boolean>> => {
    try {
        const permissions = permissionTypes.map(getPermission);
        const results = await checkMultiple(permissions);

        return permissionTypes.reduce<Record<PermissionType, boolean>>((acc, type, index) => {
            acc[type] = results[permissions[index]] === RESULTS.GRANTED;
            return acc;
        }, {} as Record<PermissionType, boolean>);
    } catch (error) {
        console.error('Error checking multiple permissions:', error);
        return permissionTypes.reduce<Record<PermissionType, boolean>>((acc, type) => {
            acc[type] = false;
            return acc;
        }, {} as Record<PermissionType, boolean>);
    }
};

/**
 * Request a permission
 * @param permissionType The type of permission to request
 * @returns True if the permission is granted after the request
 */
export const requestPermission = async (permissionType: PermissionType): Promise<boolean> => {
    try {
        if (permissionType === 'notification' && Platform.OS === 'ios') {
            return requestNotificationPermission();
        }

        const permission = getPermission(permissionType);
        const result = await request(permission);
        await markPermissionRequested(permissionType);
        return result === RESULTS.GRANTED;
    } catch (error) {
        console.error(`Error requesting ${permissionType} permission:`, error);
        return false;
    }
};

/**
 * Request multiple permissions at once
 * @param permissionTypes Array of permission types to request
 * @returns Object with permission types as keys and boolean values indicating if granted
 */
export const requestPermissions = async (permissionTypes: PermissionType[]): Promise<Record<PermissionType, boolean>> => {
    try {
        const permissions = permissionTypes.map(getPermission);
        const results = await requestMultiple(permissions);

        // Mark all permissions as requested
        await Promise.all(permissionTypes.map(markPermissionRequested));

        return permissionTypes.reduce<Record<PermissionType, boolean>>((acc, type, index) => {
            acc[type] = results[permissions[index]] === RESULTS.GRANTED;
            return acc;
        }, {} as Record<PermissionType, boolean>);
    } catch (error) {
        console.error('Error requesting multiple permissions:', error);
        return permissionTypes.reduce<Record<PermissionType, boolean>>((acc, type) => {
            acc[type] = false;
            return acc;
        }, {} as Record<PermissionType, boolean>);
    }
};

/**
 * Show a permission denied alert with an option to open settings
 * @param title Alert title
 * @param message Alert message
 */
export const showPermissionDeniedAlert = (
    title: string = 'Quyền truy cập bị từ chối',
    message: string = 'Vui lòng cấp quyền trong phần cài đặt để sử dụng tính năng này'
): void => {
    Alert.alert(
        title,
        message,
        [
            { text: 'Hủy', style: 'cancel' },
            { text: 'Đi đến Cài đặt', onPress: () => openSettings() },
        ],
        { cancelable: true }
    );
};

/**
 * Check and request a permission with proper first-time vs subsequent request handling
 * @param permissionType The type of permission
 * @param options Configuration options
 * @returns True if permission is granted
 */
export const checkAndRequestPermission = async (
    permissionType: PermissionType,
    options: {
        showAlert?: boolean;
        alertTitle?: string;
        alertMessage?: string;
        onGranted?: () => void;
        onDenied?: () => void;
    } = {}
): Promise<boolean> => {
    const {
        showAlert = true,
        alertTitle,
        alertMessage,
        onGranted,
        onDenied,
    } = options;

    // First check if permission is already granted
    const isGranted = await checkPermission(permissionType);
    if (isGranted) {
        onGranted?.();
        return true;
    }

    // Check if this is first time requesting
    const hasRequested = await hasRequestedPermission(permissionType);

    if (!hasRequested) {
        // First time request - ask directly
        const granted = await requestPermission(permissionType);
        if (granted) {
            onGranted?.();
            return true;
        } else {
            onDenied?.();
            return false;
        }
    } else {
        // Subsequent request - show settings alert
        if (showAlert) {
            showPermissionDeniedAlert(alertTitle, alertMessage);
        }
        onDenied?.();
        return false;
    }
};

/**
 * Check for all app permissions at app startup
 * This is useful to call when app starts to initialize permission state
 */
export const checkAppPermissions = async (): Promise<Record<PermissionType, boolean>> => {
    // Add or remove permission types as needed for your app
    const permissionTypes: PermissionType[] = [
        'camera',
        'location',
        'notification',
        'storage',
    ];

    return await checkPermissions(permissionTypes);
};

export default {
    checkPermission,
    checkPermissions,
    requestPermission,
    requestPermissions,
    checkAndRequestPermission,
    showPermissionDeniedAlert,
    checkAppPermissions,
    checkNotificationPermission,
    requestNotificationPermission,
}; 
import React, { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { NavigationContainer } from '@react-navigation/native';
import { selectTheme } from '@/redux/slices/themeSlice';
import { StatusBar } from 'react-native';
import { ThemeOptions } from '@/types';
import { STATUS_BAR, THEME, DefaultTheme, DarkTheme } from '@/constants/UI/themes';

const withThemeProvider = (AppNavigator: React.ComponentType) => {
    return function WithThemeProvider(props: any) {
        const themeMode: ThemeOptions = useSelector(selectTheme);

        const theme = useMemo(() => {
            return themeMode === THEME.DARK ? DarkTheme : DefaultTheme;
        }, [themeMode]);

        return (
            <NavigationContainer theme={theme}>
                <StatusBar barStyle={themeMode === THEME.DARK ? STATUS_BAR.LIGHT : STATUS_BAR.DARK} />
                <AppNavigator {...props} />
            </NavigationContainer>
        );
    };
};

export default withThemeProvider;

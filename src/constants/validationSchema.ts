// validationSchema.ts
import * as yup from 'yup';
import i18next from 'i18next';
import i18n from '@/i18n/i18n';

const createValidationSchema = () => ({
    loginSchema: yup.object().shape({
        url: yup
            .string()
            .required(i18next.t('validation.required', { field: i18next.t('fields.url.label') }))
            .test('is-valid-domain', i18next.t('validation.url.invalid'), (value) => {
                // Allow simple domain patterns with TLD
                // Accepts: example.com, my-site.co, subdomain.example.org, etc.
                const domainPattern = /^[a-zA-Z0-9][-a-zA-Z0-9.]*\.[a-zA-Z]{2,}$/;

                // Remove any https:// prefix if somehow included
                const cleanValue = value.replace(/^https?:\/\//, '');

                return domainPattern.test(cleanValue);
            }),
        email: yup
            .string()
            .required(i18next.t('validation.required', { field: i18next.t('fields.email.label') }))
            .email(i18next.t('validation.email.invalid')),
        password: yup
            .string()
            .required(i18next.t('validation.required', { field: i18next.t('fields.password.label') }))
            .min(12, i18next.t('validation.password.tooShort', { count: 12 }))
            .matches(
                /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*])[A-Za-z\d!@#$%^&*]{12,}$/,
                i18next.t('validation.password.complexity')
            ),
    }),

    forgotPasswordSchema: yup.object().shape({
        url: yup
            .string()
            .required(i18next.t('validation.required', { field: i18next.t('fields.url.label') }))
            .test('is-valid-domain', i18next.t('validation.url.invalid'), (value) => {
                // Allow simple domain patterns with TLD
                // Accepts: example.com, my-site.co, subdomain.example.org, etc.
                const domainPattern = /^[a-zA-Z0-9][-a-zA-Z0-9.]*\.[a-zA-Z]{2,}$/;

                // Remove any https:// prefix if somehow included
                const cleanValue = value.replace(/^https?:\/\//, '');

                return domainPattern.test(cleanValue);
            }),
        email: yup
            .string()
            .required(i18next.t('validation.required', { field: i18next.t('fields.email.label') }))
            .email(i18next.t('validation.email.invalid')),
    }),

    changePasswordSchema: yup.object().shape({
        currentPassword: yup
            .string()
            .required(i18next.t('validation.required', { field: i18next.t('fields.currentPassword.label') })),
        newPassword: yup
            .string()
            .required(i18next.t('validation.required', { field: i18next.t('fields.newPassword.label') }))
            .min(12, i18next.t('validation.password.tooShort', { count: 12 }))
            .matches(
                /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*])[A-Za-z\d!@#$%^&*]{12,}$/,
                i18next.t('validation.password.complexity')
            )
            .notOneOf([yup.ref('currentPassword')], i18next.t('validation.password.notSameAsCurrent')),
        confirmPassword: yup
            .string()
            .required(i18next.t('validation.required', { field: i18next.t('fields.confirmPassword.label') }))
            .oneOf([yup.ref('newPassword')], i18next.t('validation.password.mustMatch')),
    })
});

export const { loginSchema, forgotPasswordSchema, changePasswordSchema } = createValidationSchema();

// Re-export a function to recreate schemas when language changes
export const updateValidationSchemas = () => {
    const { loginSchema: newLoginSchema, forgotPasswordSchema: newForgotSchema, changePasswordSchema: newChangePasswordSchema } = createValidationSchema();
    Object.assign(loginSchema, newLoginSchema);
    Object.assign(forgotPasswordSchema, newForgotSchema);
    Object.assign(changePasswordSchema, newChangePasswordSchema);
};
import React from 'react';
import { StyleSheet, View, Text, TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '@react-navigation/native';
import CCard from './CCard';
import CIcon from './CIcon';
import { SPACING, COLORS } from '../constants/UI/themes';
import { Isetting } from '@/constants/UI/icons';
import { Task } from '@/interfaces/task';
import { DrawerNavigationProp } from '@react-navigation/drawer';
import { DrawerParamList, StackParamList } from '@/types/navigation';
import { SCREEN_NAMES } from '@/constants/navigation';

interface TaskItemProps {
    item: Task;
}

type TaskNavigationProp = DrawerNavigationProp<DrawerParamList, 'TaskDetail'>;

const TaskItem = ({ item }: TaskItemProps) => {
    const theme = useTheme();
    const navigation = useNavigation<TaskNavigationProp>();
    const estimatedTime = '30 mins';
    const distance = '2.5 miles';

    const handleTaskPress = () => {
        navigation.navigate(SCREEN_NAMES.DETAIL_TASK as any, { task: item });
    };

    return (
        <TouchableOpacity
            style={styles.itemWrapper}
            onPress={handleTaskPress}
            activeOpacity={0.7}
        >
            <CCard style={styles.card}>
                <View style={styles.statusContainer}>
                    <Text style={[styles.status, { color: theme.colors.primary }]}>
                        {item.status === 'scheduled' ? 'Pending' : item.status === 'completed' ? 'Complete' : 'In Progress'}
                    </Text>
                </View>

                <Text style={styles.taskCode}>{item.task_code}</Text>

                <View style={styles.customerInfo}>
                    <CIcon source={Isetting} size={20} tintColor={theme.colors.text} />
                    <Text style={styles.customerName}>{item.customer.name}</Text>
                </View>

                <View style={styles.productsContainer}>
                    <CIcon source={Isetting} size={20} tintColor={theme.colors.text} />
                    <Text style={styles.productsText}>
                        {item.products
                            ? (item.products.length > 2
                                ? `${item.products[0]}, ${item.products[1]}...`
                                : item.products.join(', '))
                            : item.repair_product}
                    </Text>
                </View>

                <View style={styles.footer}>
                    <Text style={styles.timeText}>{estimatedTime}</Text>
                    <Text style={styles.distanceText}>{distance}</Text>
                </View>
            </CCard>
        </TouchableOpacity>
    );
};

const styles = StyleSheet.create({
    itemWrapper: {
        width: '100%',
        alignItems: 'center',
    },
    card: {
        width: 380,
        height: 173,
        backgroundColor: COLORS.light.white,
        padding: SPACING.md,
        borderRadius: 8,
    },
    statusContainer: {
        position: 'absolute',
        top: SPACING.md,
        right: SPACING.md,
    },
    status: {
        fontSize: 14,
        fontWeight: '600',
    },
    taskCode: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: SPACING.sm,
    },
    customerInfo: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: SPACING.xs,
    },
    customerName: {
        marginLeft: SPACING.xs,
        fontSize: 16,
    },
    productsContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: SPACING.sm,
    },
    productsText: {
        marginLeft: SPACING.xs,
        fontSize: 14,
        flex: 1,
    },
    footer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: 'auto',
    },
    timeText: {
        fontSize: 14,
        color: COLORS.light.border,
    },
    distanceText: {
        fontSize: 14,
        color: COLORS.light.border,
    },
});

export default TaskItem; 
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>$(APP_NAME)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSCalendarsFullAccessUsageDescription</key>
	<string>$(PRODUCT_NAME) requires full access to your calendar to manage and display your scheduled events.</string>
	<key>NSCalendarsWriteOnlyAccessUsageDescription</key>
	<string>$(PRODUCT_NAME) needs write access to your calendar to add events without reading other details.</string>
	<key>NSCameraUsageDescription</key>
	<string>$(PRODUCT_NAME) requires camera access to take photos and scan QR codes.</string>
	<key>NSContactsUsageDescription</key>
	<string>$(PRODUCT_NAME) needs access to your contacts to find and connect with people in your network.</string>
	<key>NSFaceIDUsageDescription</key>
	<string>$(PRODUCT_NAME) uses Face ID for secure authentication and quick login.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>$(PRODUCT_NAME) needs location access to provide location-based services and navigation features.</string>
	<key>NSLocationTemporaryUsageDescriptionDictionary</key>
	<dict>
		<key>YOUR-PURPOSE-KEY</key>
		<string>$(PRODUCT_NAME) temporarily requires location access for specific features like real-time tracking.</string>
	</dict>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>$(PRODUCT_NAME) needs location access while using the app to provide location-based services.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>$(PRODUCT_NAME) needs permission to add photos to save images to your gallery.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>$(PRODUCT_NAME) requires access to your photo library to select and upload images.</string>
	<key>NSUserNotificationsUsageDescription</key>
	<string>$(PRODUCT_NAME) would like to send you notifications for important updates and messages.</string>
	<key>UIBackgroundModes</key>
	<array>
		<string>location</string>
		<string>processing</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>

import React, { useEffect, useRef, useState } from 'react';
import { View, StyleSheet, TouchableOpacity, Linking, Platform } from 'react-native';
import MapView, { <PERSON><PERSON>, <PERSON>yline, PROVIDER_GOOGLE, Region } from 'react-native-maps';
import { useTheme } from '@react-navigation/native';
import CText from '../CText';
import CIcon from '../CIcon';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import { getDistanceText, getEstimatedTimeText } from '../../utils/mapUtils';

export interface PointLocation {
    id: string;
    latitude: number;
    longitude: number;
    title?: string;
    description?: string;
    customer?: {
        name: string;
        phone?: string;
    };
    address?: string;
    taskDetails?: any; // Task details (can be changed to TaskType if available)
    index?: number;
}

interface Route {
    distance: number; // meters
    duration: number; // seconds
    coordinates: { latitude: number; longitude: number }[];
}

interface MapViewComponentProps {
    points: PointLocation[];
    userLocation?: { latitude: number; longitude: number } | null;
    initialRegion?: Region;
    onPointPress?: (point: PointLocation) => void;
    route?: Route | null;
    isMultiRoute?: boolean;
    onUpdateRoute?: () => void;
    selectedPointId?: string | null;
}

const MapViewComponent: React.FC<MapViewComponentProps> = ({
    points,
    userLocation,
    initialRegion,
    onPointPress,
    route,
    isMultiRoute = false,
    onUpdateRoute,
    selectedPointId,
}) => {
    const { colors } = useTheme();
    const mapRef = useRef<MapView | null>(null);
    const bottomSheetRef = useRef<BottomSheetModal>(null);
    const [selectedPoint, setSelectedPoint] = useState<PointLocation | null>(null);

    // Default region (Vietnam)
    const defaultRegion = {
        latitude: 10.762622,
        longitude: 106.660172,
        latitudeDelta: 0.0922,
        longitudeDelta: 0.0421,
    };

    useEffect(() => {
        // If a point is selected from outside, find and select that point
        if (selectedPointId) {
            const point = points.find(p => p.id === selectedPointId);
            if (point) {
                handleMarkerPress(point);
            }
        }
    }, [selectedPointId, points]);

    // Handle marker press
    const handleMarkerPress = (point: PointLocation) => {
        setSelectedPoint(point);

        // Zoom to selected point
        mapRef.current?.animateToRegion({
            latitude: point.latitude,
            longitude: point.longitude,
            latitudeDelta: 0.01,
            longitudeDelta: 0.01,
        }, 500);

        // Open bottom sheet
        if (onPointPress) {
            onPointPress(point);
        }
    };

    // Open Google Maps
    const openGoogleMaps = () => {
        if (!route || !selectedPoint) return;

        // Create URL for Google Maps
        const destination = `${selectedPoint.latitude},${selectedPoint.longitude}`;
        let url: string;

        // If user location exists, create URL with start point
        if (userLocation) {
            const origin = `${userLocation.latitude},${userLocation.longitude}`;
            url = `https://www.google.com/maps/dir/?api=1&origin=${origin}&destination=${destination}&travelmode=driving`;
        } else {
            // If no user location, just navigate to destination
            url = `https://www.google.com/maps/search/?api=1&query=${destination}`;
        }

        Linking.canOpenURL(url).then(supported => {
            if (supported) {
                Linking.openURL(url);
            } else {
                console.log('Cannot open Google Maps');
            }
        });
    };

    return (
        <View style={styles.container}>
            <MapView
                ref={mapRef}
                provider={PROVIDER_GOOGLE}
                style={styles.map}
                initialRegion={initialRegion || defaultRegion}
                showsUserLocation={true}
                showsMyLocationButton={true}
                showsCompass={true}
                showsScale={true}
            >
                {/* Display points on the map */}
                {points.map((point, index) => (
                    <Marker
                        key={point.id}
                        coordinate={{
                            latitude: point.latitude,
                            longitude: point.longitude
                        }}
                        title={point.title || `Point ${index + 1}`}
                        description={point.description}
                        pinColor={selectedPoint?.id === point.id ? 'blue' : (index === 0 ? 'red' : 'orange')}
                        onPress={() => handleMarkerPress(point)}
                    />
                ))}

                {/* Display user location (if available) */}
                {userLocation && (
                    <Marker
                        coordinate={{
                            latitude: userLocation.latitude,
                            longitude: userLocation.longitude
                        }}
                        title="Your location"
                        pinColor="blue"
                    />
                )}

                {/* Display route */}
                {route && route.coordinates && route.coordinates.length > 0 && (
                    <Polyline
                        coordinates={route.coordinates}
                        strokeWidth={4}
                        strokeColor={colors.primary}
                    />
                )}
            </MapView>

            {/* Route information */}
            {route && selectedPoint && (
                <View style={[styles.routeInfo, { backgroundColor: colors.card }]}>
                    <View style={styles.routeDetails}>
                        <CText style={[styles.routeText, { color: colors.text }]}>
                            Distance: {getDistanceText(route.distance)}
                        </CText>
                        <CText style={[styles.routeText, { color: colors.text }]}>
                            Estimated time: {getEstimatedTimeText(route.duration)}
                        </CText>
                    </View>
                    <View style={styles.actionButtons}>
                        <TouchableOpacity
                            style={[styles.actionButton, { backgroundColor: colors.primary }]}
                            onPress={onUpdateRoute}
                        >
                            <CText style={[styles.buttonText, { color: 'white' }]}>Update</CText>
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={[styles.actionButton, { backgroundColor: '#4285F4' }]}
                            onPress={openGoogleMaps}
                        >
                            <CText style={[styles.buttonText, { color: 'white' }]}>Google Maps</CText>
                        </TouchableOpacity>
                    </View>
                </View>
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        position: 'relative',
    },
    map: {
        flex: 1,
    },
    routeInfo: {
        position: 'absolute',
        bottom: 20,
        left: 10,
        right: 10,
        padding: 10,
        borderRadius: 8,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.2,
        shadowRadius: 2,
        elevation: 3,
    },
    routeDetails: {
        marginBottom: 8,
    },
    routeText: {
        fontSize: 14,
        marginBottom: 4,
    },
    actionButtons: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    actionButton: {
        flex: 1,
        paddingVertical: 8,
        paddingHorizontal: 10,
        borderRadius: 4,
        alignItems: 'center',
        marginHorizontal: 4,
    },
    buttonText: {
        fontSize: 14,
        fontWeight: 'bold',
    },
});

export default MapViewComponent; 
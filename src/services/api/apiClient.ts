import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError, CancelTokenSource, InternalAxiosRequestConfig } from 'axios';
import { Platform, AppState, AppStateStatus } from 'react-native';
import * as authUtils from '@/utils/authUtils';
import { useToast } from '@/hocs/toast';
import i18n from '@/i18n/i18n';
import { API_TIMEOUT, DEFAULT_BASE_URL } from '@/constants/api';
import authService from '../authService';

// Types
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    message?: string;
    status?: number;
    error?: any;
}

// Store pending requests when refreshing token
let isRefreshing = false;
let refreshSubscribers: ((token: string) => void)[] = [];

// Active request tokens for cancellation
const pendingRequests: Map<string, CancelTokenSource> = new Map();

// Custom interface to extend InternalAxiosRequestConfig
interface CustomRequestConfig extends InternalAxiosRequestConfig {
    _retry?: boolean;
}

/**
 * ApiClient is a singleton class that provides methods for making API requests
 * with built-in error handling, token management, and request cancellation.
 */
class ApiClient {
    private static instance: ApiClient;
    private axiosInstance: AxiosInstance;
    private baseURL: string = DEFAULT_BASE_URL;
    private appStateSubscription: any = null;

    private constructor() {
        this.axiosInstance = this.createAxiosInstance();
        this.setupInterceptors();
        this.monitorAppState();
    }

    /**
     * Get the singleton instance of ApiClient
     */
    public static getInstance(): ApiClient {
        if (!ApiClient.instance) {
            ApiClient.instance = new ApiClient();
        }
        return ApiClient.instance;
    }

    /**
     * Create a new Axios instance with default configuration
     */
    private createAxiosInstance(): AxiosInstance {
        return axios.create({
            baseURL: this.baseURL,
            timeout: API_TIMEOUT,
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'X-Platform': Platform.OS,
                'X-Platform-Version': Platform.Version.toString(),
            },
        });
    }

    /**
     * Setup Axios request and response interceptors
     */
    private setupInterceptors(): void {
        // Request interceptor
        this.axiosInstance.interceptors.request.use(
            async (config: InternalAxiosRequestConfig) => {
                // Get current base URL
                config.baseURL = this.baseURL;

                // Add auth token if available
                try {
                    const token = await authUtils.getToken();
                    if (token) {
                        config.headers.Authorization = `Bearer ${token}`;
                    }
                } catch (error) {
                    console.error('Error getting auth token:', error);
                }

                // Set up request cancellation
                const requestId = `${config.method}-${config.url}-${Date.now()}`;
                const source = axios.CancelToken.source();
                config.cancelToken = source.token;
                pendingRequests.set(requestId, source);

                // Log request as curl command (for debugging)
                if (__DEV__) {
                    console.log('API Request as curl:', this.requestToCurl(config));
                }

                return config;
            },
            (error) => {
                return Promise.reject(error);
            }
        );

        // Response interceptor
        this.axiosInstance.interceptors.response.use(
            (response: AxiosResponse) => {
                // Format successful responses
                const apiResponse = this.formatResponse(response);
                return apiResponse as unknown as AxiosResponse;
            },
            async (error) => {
                // Handle error with proper typing
                return Promise.reject(await this.handleRequestError(error));
            }
        );
    }

    /**
     * Handle request errors, including token refresh for 401 errors
     */
    private async handleRequestError(error: unknown): Promise<ApiResponse> {
        // Early return for non-Axios errors

        if (!axios.isAxiosError(error)) {
            console.log('case 4 ::: ', error)
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Unknown error',
                error
            };
        }

        // Now we know error is an AxiosError
        const axiosError = error as AxiosError<unknown, unknown> | any;

        if (axios.isCancel(axiosError)) {
            // Handle cancelled requests
            return {
                success: false,
                message: 'Request was cancelled',
                status: 499, // Client Closed Request
                error: axiosError
            };
        }

        // Safely access config and response properties
        const originalRequest = axiosError.config as CustomRequestConfig | undefined;
        const status = axiosError.response?.status;

        // Handle authentication errors (401)
        if (status === 401 && originalRequest && !originalRequest._retry) {
            if (isRefreshing) {
                // Wait for token refresh
                try {
                    const newToken = await new Promise<string>((resolve) => {
                        refreshSubscribers.push(resolve);
                    });
                    if (originalRequest.headers) {
                        originalRequest.headers.Authorization = `Bearer ${newToken}`;
                    }
                    return this.axiosInstance(originalRequest) as unknown as ApiResponse;
                } catch (refreshError) {
                    return this.formatError(axiosError, 'Session expired. Please log in again.');
                }
            }

            // Mark as retrying and try to refresh token
            originalRequest._retry = true;
            isRefreshing = true;

            try {
                // Attempt to refresh the token
                const refreshToken = await authUtils.getRefreshToken();
                if (!refreshToken) {
                    throw new Error('No refresh token available');
                }

                // Call token refresh service
                const response = await authService.refreshToken(refreshToken);

                if (!response.success || !response.data.access_token) {
                    throw new Error('Token refresh failed');
                }

                const { data } = response;

                // Save the new token
                await authUtils.saveToken(data.access_token, data.refresh_token);

                // Update authorization headers
                this.axiosInstance.defaults.headers.common.Authorization = `Bearer ${data.access_token}`;
                if (originalRequest.headers) {
                    originalRequest.headers.Authorization = `Bearer ${data.access_token}`;
                }

                // Notify all pending requests
                refreshSubscribers.forEach((callback) => callback(data.access_token));
                refreshSubscribers = [];

                // Retry the original request
                return this.axiosInstance(originalRequest) as unknown as ApiResponse;
            } catch (refreshError) {
                // Handle token refresh failure
                console.error('Token refresh failed:', refreshError);

                // Notify waiting requests about the failure
                refreshSubscribers.forEach((callback) => callback(''));
                refreshSubscribers = [];

                // Force logout
                authUtils.clearTokens();

                return this.formatError(axiosError, 'Session expired. Please log in again.');
            } finally {
                isRefreshing = false;
            }
        }

        // Handle other errors
        return this.formatError(axiosError);
    }

    /**
     * Format axios error into standard API response
     */
    private formatError(error: unknown, customMessage?: string): ApiResponse {
        let status = 500;
        let message = customMessage || 'An unexpected error occurred';

        if (axios.isAxiosError(error)) {
            status = error.response?.status || 500;

            // Process error response if available
            if (error.response?.data) {
                const data = error.response.data as any;
                message = data.message || data.error || message;
            } else if (error.message === 'Network Error') {
                message = i18n.t('errors.networkError');
            }
        } else if (error instanceof Error) {
            message = error.message;
        }

        return {
            success: false,
            status,
            message,
            error: axios.isAxiosError(error) ? error.response?.data || error.message : error
        };
    }

    /**
     * Format successful response into standard API response
     */
    private formatResponse(response: AxiosResponse): ApiResponse {
        const data = response.data;

        // If the API already returns in our format, use it directly
        if (data && (typeof data.success === 'boolean')) {
            return {
                ...data,
                status: response.status
            };
        }

        // Otherwise, wrap the response in our standard format
        return {
            success: true,
            data,
            status: response.status,
            message: 'Success'
        };
    }

    /**
     * Set the base URL for API requests
     */
    public setBaseUrl(url: string): void {
        if (!url.startsWith('http')) {
            this.baseURL = `https://${url}`;
        } else {
            this.baseURL = url;
        }
        this.axiosInstance.defaults.baseURL = this.baseURL;
    }

    /**
     * Monitor app state changes to cancel pending requests when app is inactive
     */
    private monitorAppState(): void {
        this.appStateSubscription = AppState.addEventListener('change', (nextAppState: AppStateStatus) => {
            if (nextAppState !== 'active') {
                // Cancel all pending requests when app goes to background
                this.cancelAllRequests('App state changed to ' + nextAppState);
            }
        });
    }

    /**
     * Cancel all pending requests
     */
    public cancelAllRequests(reason: string = 'Operation cancelled'): void {
        pendingRequests.forEach((source) => {
            source.cancel(reason);
        });
        pendingRequests.clear();
    }

    /**
     * Clean up resources
     */
    public cleanup(): void {
        if (this.appStateSubscription) {
            this.appStateSubscription.remove();
        }
        this.cancelAllRequests('API client cleanup');
    }

    /**
     * Make a GET request
     */
    public async get<T = any>(
        endpoint: string,
        params?: any,
        config?: AxiosRequestConfig
    ): Promise<ApiResponse<T>> {
        try {
            const response = await this.axiosInstance.get<T>(endpoint, {
                params,
                ...config,
            });
            return this.formatResponse(response) as ApiResponse<T>;
        } catch (error) {
            console.log('case 5 ::: get error', error)
            if (error instanceof Error) {
                if (axios.isAxiosError(error)) {
                    return this.formatError(error as AxiosError<any, any>);
                }
                return {
                    success: false,
                    message: error.message,
                    error: error
                };
            }
            return {
                success: false,
                message: 'Unknown error occurred',
                error: error
            };
        }
    }

    /**
     * Make a POST request
     */
    public async post<T = any>(
        endpoint: string,
        data?: any,
        config?: AxiosRequestConfig
    ): Promise<ApiResponse<T>> {
        try {
            const response = await this.axiosInstance.post<T>(endpoint, data, config);
            return this.formatResponse(response) as ApiResponse<T>;
        } catch (error) {
            if (error instanceof Error) {
                if (axios.isAxiosError(error)) {
                    console.log('case 1 ::: ', error)
                    return this.formatError(error as AxiosError<any, any>);
                }
                console.log('case 2 ::: ', error)
                return {
                    success: false,
                    message: error.message,
                    ...(error as any),
                };
            }
            console.log('case 3 ::: ', error)
            return {
                ...(error as any),
                success: false,
                message: 'Unknown error occurred',
            };
        }
    }

    /**
     * Make a PUT request
     */
    public async put<T = any>(
        endpoint: string,
        data?: any,
        config?: AxiosRequestConfig
    ): Promise<ApiResponse<T>> {
        try {
            const response = await this.axiosInstance.put<T>(endpoint, data, config);
            return this.formatResponse(response) as ApiResponse<T>;
        } catch (error) {
            if (error instanceof Error) {
                if (axios.isAxiosError(error)) {
                    return this.formatError(error as AxiosError<any, any>);
                }
                return {
                    success: false,
                    message: error.message,
                    error: error
                };
            }
            return {
                success: false,
                message: 'Unknown error occurred',
                error: error
            };
        }
    }

    /**
     * Make a PATCH request
     */
    public async patch<T = any>(
        endpoint: string,
        data?: any,
        config?: AxiosRequestConfig
    ): Promise<ApiResponse<T>> {
        try {
            const response = await this.axiosInstance.patch<T>(endpoint, data, config);
            return this.formatResponse(response) as ApiResponse<T>;
        } catch (error) {
            if (error instanceof Error) {
                if (axios.isAxiosError(error)) {
                    return this.formatError(error as AxiosError<any, any>);
                }
                return {
                    success: false,
                    message: error.message,
                    error: error
                };
            }
            return {
                success: false,
                message: 'Unknown error occurred',
                error: error
            };
        }
    }

    /**
     * Make a DELETE request
     */
    public async delete<T = any>(
        endpoint: string,
        config?: AxiosRequestConfig
    ): Promise<ApiResponse<T>> {
        try {
            const response = await this.axiosInstance.delete<T>(endpoint, config);
            return this.formatResponse(response) as ApiResponse<T>;
        } catch (error) {
            if (error instanceof Error) {
                if (axios.isAxiosError(error)) {
                    return this.formatError(error as AxiosError<any, any>);
                }
                return {
                    success: false,
                    message: error.message,
                    error: error
                };
            }
            return {
                success: false,
                message: 'Unknown error occurred',
                error: error
            };
        }
    }

    /**
     * Upload a file with progress tracking
     */
    public async uploadFile<T = any>(
        endpoint: string,
        file: any,
        onProgress?: (percentCompleted: number) => void,
        config?: AxiosRequestConfig
    ): Promise<ApiResponse<T>> {
        const formData = new FormData();

        // If file is an object with uri, add it to FormData
        if (file.uri) {
            const fileType = file.type || 'application/octet-stream';
            const fileName = file.name || file.uri.split('/').pop() || 'file';

            formData.append('file', {
                uri: file.uri,
                type: fileType,
                name: fileName,
            } as any);
        } else if (Array.isArray(file)) {
            // If file is an array, add each file to FormData
            file.forEach((f, index) => {
                const fileType = f.type || 'application/octet-stream';
                const fileName = f.name || f.uri.split('/').pop() || `file${index}`;

                formData.append('files', {
                    uri: f.uri,
                    type: fileType,
                    name: fileName,
                } as any);
            });
        }

        // Add any additional fields to the form data
        if (config?.data) {
            Object.keys(config.data).forEach(key => {
                formData.append(key, config.data[key]);
            });
        }

        try {
            const response = await this.axiosInstance.post<T>(endpoint, formData, {
                ...config,
                headers: {
                    ...config?.headers,
                    'Content-Type': 'multipart/form-data',
                },
                onUploadProgress: (progressEvent) => {
                    if (onProgress && progressEvent.total) {
                        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                        onProgress(percentCompleted);
                    }
                },
            });

            return this.formatResponse(response) as ApiResponse<T>;
        } catch (error) {
            if (error instanceof Error) {
                if (axios.isAxiosError(error)) {
                    return this.formatError(error as AxiosError<any, any>);
                }
                return {
                    success: false,
                    message: error.message,
                    error: error
                };
            }
            return {
                success: false,
                message: 'Unknown error occurred',
                error: error
            };
        }
    }

    private requestToCurl(config: InternalAxiosRequestConfig): string {
        const { method, url, headers, data, baseURL, params } = config;

        let fullUrl = (baseURL || '') + (url || '');

        if (params) {
            const queryParams = new URLSearchParams();
            Object.entries(params).forEach(([key, value]) => {
                queryParams.append(key, String(value));
            });
            const queryString = queryParams.toString();
            if (queryString) {
                fullUrl += (fullUrl.includes('?') ? '&' : '?') + queryString;
            }
        }

        let curl = `curl -X ${method?.toUpperCase() || 'GET'} '${fullUrl}'`;

        if (headers) {
            Object.entries(headers).forEach(([key, value]) => {
                if (key.toLowerCase() !== 'common' && key.toLowerCase() !== 'delete' &&
                    key.toLowerCase() !== 'get' && key.toLowerCase() !== 'head' &&
                    key.toLowerCase() !== 'post' && key.toLowerCase() !== 'put' &&
                    key.toLowerCase() !== 'patch' && value !== undefined) {
                    curl += ` -H '${key}: ${value}'`;
                }
            });
        }

        if (data) {
            let dataStr;

            if (typeof data === 'string') {
                dataStr = data;
            } else if (data instanceof FormData) {
                curl += ` -F 'data=@form-data'`;
                return curl;
            } else if (typeof data === 'object') {
                try {
                    dataStr = JSON.stringify(data);
                } catch (e) {
                    dataStr = '[Complex Object]';
                }
            } else {
                dataStr = String(data);
            }

            curl += ` -d '${dataStr.replace(/'/g, "\\'")}'`;
        }

        return curl;
    }
}

// Export a singleton instance
export const apiClient = ApiClient.getInstance();

// Export a hook for using the API client in components
export const useApi = () => {
    const toast = useToast();

    const handleApiError = (error: any) => {
        if (error && error.message) {
            toast.showToast(error.message, 'error');
        } else {
            toast.showToast('toast.networkError', 'error');
        }
    };

    return {
        apiClient,
        handleApiError,
    };
};

export default apiClient; 
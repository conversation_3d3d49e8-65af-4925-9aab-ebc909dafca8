import React from 'react';
import { TextInput, TextInputProps, StyleSheet, ViewStyle, TextStyle } from 'react-native';
import { useTheme } from '@react-navigation/native';
import { AppTheme } from '@/types';
import { FONT_FAMILY } from '@/constants/UI/themes';

interface CInputProps extends TextInputProps {
    contentStyle?: TextStyle;
}

const CInput: React.FC<CInputProps> = ({ style, contentStyle, ...props }) => {
    const theme = useTheme() as unknown as AppTheme;

    return (
        <TextInput
            style={[
                styles.input,
                {
                    backgroundColor: theme.colors.surface,
                    borderRadius: theme.sizes.borderRadius.small,
                    height: theme.sizes.inputHeight,
                    color: theme.colors.text,
                    paddingHorizontal: theme.spacing.sm,
                    fontFamily: FONT_FAMILY.regular,
                },
                contentStyle,
                style,
            ]}
            placeholderTextColor={theme.colors.placeholder}
            {...props}
        />
    );
};

const styles = StyleSheet.create({
    input: {
        width: '100%',
    },
});

export default CInput;
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { View, StyleSheet, ActivityIndicator } from 'react-native';
import { useTheme } from '@react-navigation/native';
import { BottomSheetModal, BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { PointLocation } from '@/components/Map/MapView';
import MapViewComponent from '@/components/Map/MapView';
import MapPointDetails from '@/components/Map/MapPointDetails';
import useMapDirections from '@/hooks/useMapDirections';
import { getDistanceText, getEstimatedTimeText } from '@/utils/mapUtils';
import { GestureHandlerRootView } from 'react-native-gesture-handler';

interface SingleMapScreenProps {
    // Information about the destination point
    destinationPoint: PointLocation;
    // Callback when going back
    onGoBack?: () => void;
}

/**
 * Single Map Screen - Displays one point and directions to that point
 */
const SingleMapScreen: React.FC<SingleMapScreenProps> = ({
    destinationPoint,
    onGoBack,
}) => {
    const { colors } = useTheme();
    const bottomSheetRef = useRef<BottomSheetModal>(null);
    const [detailVisible, setDetailVisible] = useState(false);

    // Create an array with just one destination point
    const points = [destinationPoint];

    // Use the navigation hook
    const {
        userLocation,
        getCurrentLocation,
        currentRoute,
        updateRoute,
        loading,
        error,
    } = useMapDirections({
        points,
        isMultiRoute: false,
    });

    // Open bottom sheet when pressing a marker
    const handlePointPress = useCallback((point: PointLocation) => {
        bottomSheetRef.current?.present();
        setDetailVisible(true);
    }, []);

    // Close bottom sheet
    const handleCloseBottomSheet = useCallback(() => {
        bottomSheetRef.current?.dismiss();
        setDetailVisible(false);
    }, []);

    // Update current location and route
    const handleUpdateRoute = useCallback(() => {
        getCurrentLocation();
        updateRoute();
    }, [getCurrentLocation, updateRoute]);

    // Open bottom sheet when mounted
    useEffect(() => {
        if (points.length > 0) {
            setTimeout(() => {
                bottomSheetRef.current?.present();
                setDetailVisible(true);
            }, 500);
        }
    }, []);

    return (
        <GestureHandlerRootView style={styles.container}>
            <BottomSheetModalProvider>
                <View style={styles.container}>
                    {/* Display map */}
                    {/* <MapViewComponent
                        points={points}
                        userLocation={userLocation}
                        onPointPress={handlePointPress}
                        route={currentRoute}
                        onUpdateRoute={handleUpdateRoute}
                    /> */}

                    {/* Display loading status */}
                    {loading && (
                        <View style={[styles.loadingContainer, { backgroundColor: colors.background + '80' }]}>
                            <ActivityIndicator size="large" color={colors.primary} />
                        </View>
                    )}

                    {/* Bottom sheet displaying details */}
                    <BottomSheetModal
                        ref={bottomSheetRef}
                        index={0}
                        snapPoints={['30%', '50%']}
                        enablePanDownToClose={true}
                        onDismiss={handleCloseBottomSheet}
                        backgroundStyle={{ backgroundColor: colors.card }}
                        handleIndicatorStyle={{ backgroundColor: colors.border }}
                    >
                        {/* {detailVisible && currentRoute && (
                            <MapPointDetails
                                point={destinationPoint}
                                distance={currentRoute.distance}
                                duration={currentRoute.duration}
                            />
                        )} */}
                        <></>
                    </BottomSheetModal>
                </View>
            </BottomSheetModalProvider>
        </GestureHandlerRootView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    loadingContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: 'center',
        alignItems: 'center',
    },
});

export default SingleMapScreen; 
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { View, StyleSheet, ActivityIndicator, Text, TouchableOpacity, FlatList } from 'react-native';
import { useTheme } from '@react-navigation/native';
import { BottomSheetModal, BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { PointLocation } from '@/components/Map/MapView';
import MapViewComponent from '@/components/Map/MapView';
import MapPointDetails from '@/components/Map/MapPointDetails';
import useMapDirections from '@/hooks/useMapDirections';
import { calculateMapCenter, getDistanceText, getEstimatedTimeText, sortPointsByDistance } from '@/utils/mapUtils';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import CText from '@/components/CText';

interface MultiMapScreenProps {
    // List of points (tasks)
    points: PointLocation[];
    // Callback when going back
    onGoBack?: () => void;
    // Auto-sort by distance
    autoSort?: boolean;
}

/**
 * Multi-point Map Screen - Displays multiple points and the route through them
 */
const MultiMapScreen: React.FC<MultiMapScreenProps> = ({
    points: initialPoints,
    onGoBack,
    autoSort = false,
}) => {
    const { colors } = useTheme();
    const bottomSheetRef = useRef<BottomSheetModal>(null);
    const [detailVisible, setDetailVisible] = useState(false);

    // Number the points in order
    const [points, setPoints] = useState<PointLocation[]>(
        initialPoints.map((point, index) => ({ ...point, index }))
    );

    // Use the navigation hook
    const {
        userLocation,
        getCurrentLocation,
        routes,
        currentRoute,
        selectedPointIndex,
        selectPoint,
        updateRoute,
        loading,
        error,
    } = useMapDirections({
        points,
        isMultiRoute: true,
    });

    // Open bottom sheet when pressing a marker
    const handlePointPress = useCallback((point: PointLocation) => {
        const index = points.findIndex(p => p.id === point.id);
        if (index !== -1) {
            selectPoint(index);
            bottomSheetRef.current?.present();
            setDetailVisible(true);
        }
    }, [points, selectPoint]);

    // Close bottom sheet
    const handleCloseBottomSheet = useCallback(() => {
        bottomSheetRef.current?.dismiss();
        setDetailVisible(false);
    }, []);

    // Update current location and route
    const handleUpdateRoute = useCallback(() => {
        getCurrentLocation();
        updateRoute();
    }, [getCurrentLocation, updateRoute]);

    // When user location is available, sort points by distance if needed
    useEffect(() => {
        if (userLocation && autoSort && initialPoints.length > 1) {
            const sortedPoints = sortPointsByDistance(initialPoints, userLocation);
            // Ensure we preserve the id and other required properties
            setPoints(sortedPoints.map((point, index) => ({ ...point, index } as PointLocation)));
        }
    }, [userLocation, autoSort, initialPoints]);

    // Calculate total distance and time
    const totalDistance = routes.reduce((sum, route) => sum + (route?.distance || 0), 0);
    const totalDuration = routes.reduce((sum, route) => sum + (route?.duration || 0), 0);

    return (
        <GestureHandlerRootView style={styles.container}>
            <BottomSheetModalProvider>
                <View style={styles.container}>
                    {/* Display map */}
                    <MapViewComponent
                        points={points}
                        userLocation={userLocation}
                        onPointPress={handlePointPress}
                        route={currentRoute}
                        onUpdateRoute={handleUpdateRoute}
                        selectedPointId={points[selectedPointIndex]?.id}
                        isMultiRoute={true}
                        initialRegion={calculateMapCenter([
                            ...(userLocation ? [userLocation] : []),
                            ...points,
                        ])}
                    />

                    {/* General route information */}
                    {routes.length > 0 && (
                        <View style={[styles.routeSummary, { backgroundColor: colors.card }]}>
                            <View style={styles.routeSummaryContent}>
                                <View style={styles.routeInfoItem}>
                                    <CText style={[styles.routeLabel, { color: colors.text }]}>
                                        Total distance:
                                    </CText>
                                    <CText style={[styles.routeValue, { color: colors.text }]}>
                                        {getDistanceText(totalDistance)}
                                    </CText>
                                </View>
                                <View style={styles.routeInfoItem}>
                                    <CText style={[styles.routeLabel, { color: colors.text }]}>
                                        Total time:
                                    </CText>
                                    <CText style={[styles.routeValue, { color: colors.text }]}>
                                        {getEstimatedTimeText(totalDuration)}
                                    </CText>
                                </View>
                                <View style={styles.routeInfoItem}>
                                    <CText style={[styles.routeLabel, { color: colors.text }]}>
                                        Number of stops:
                                    </CText>
                                    <CText style={[styles.routeValue, { color: colors.text }]}>
                                        {points.length}
                                    </CText>
                                </View>
                            </View>
                        </View>
                    )}

                    {/* Display loading status */}
                    {loading && (
                        <View style={[styles.loadingContainer, { backgroundColor: colors.background + '80' }]}>
                            <ActivityIndicator size="large" color={colors.primary} />
                        </View>
                    )}

                    {/* Bottom sheet displaying details */}
                    <BottomSheetModal
                        ref={bottomSheetRef}
                        index={0}
                        snapPoints={['40%', '70%']}
                        enablePanDownToClose={true}
                        onDismiss={handleCloseBottomSheet}
                        backgroundStyle={{ backgroundColor: colors.card }}
                        handleIndicatorStyle={{ backgroundColor: colors.border }}
                    >
                        {detailVisible && currentRoute && selectedPointIndex < points.length && (
                            <View style={styles.bottomSheetContent}>
                                <MapPointDetails
                                    point={points[selectedPointIndex]}
                                    distance={currentRoute.distance}
                                    duration={currentRoute.duration}
                                    prevPointName={
                                        selectedPointIndex === 0
                                            ? 'Your location'
                                            : points[selectedPointIndex - 1]?.title || `Point ${selectedPointIndex}`
                                    }
                                />

                                {/* Navigation between points */}
                                {points.length > 1 && (
                                    <View style={styles.pointNavigation}>
                                        <TouchableOpacity
                                            style={[
                                                styles.navButton,
                                                { backgroundColor: colors.primary, opacity: selectedPointIndex > 0 ? 1 : 0.5 }
                                            ]}
                                            disabled={selectedPointIndex <= 0}
                                            onPress={() => selectPoint(selectedPointIndex - 1)}
                                        >
                                            <CText style={styles.navButtonText}>Previous point</CText>
                                        </TouchableOpacity>

                                        <TouchableOpacity
                                            style={[
                                                styles.navButton,
                                                {
                                                    backgroundColor: colors.primary,
                                                    opacity: selectedPointIndex < points.length - 1 ? 1 : 0.5
                                                }
                                            ]}
                                            disabled={selectedPointIndex >= points.length - 1}
                                            onPress={() => selectPoint(selectedPointIndex + 1)}
                                        >
                                            <CText style={styles.navButtonText}>Next point</CText>
                                        </TouchableOpacity>
                                    </View>
                                )}
                            </View>
                        )}
                    </BottomSheetModal>
                </View>
            </BottomSheetModalProvider>
        </GestureHandlerRootView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    loadingContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: 'center',
        alignItems: 'center',
    },
    routeSummary: {
        position: 'absolute',
        top: 16,
        left: 16,
        right: 16,
        padding: 12,
        borderRadius: 8,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    routeSummaryContent: {
        flexDirection: 'column',
    },
    routeInfoItem: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 4,
    },
    routeLabel: {
        fontSize: 14,
        fontWeight: '500',
    },
    routeValue: {
        fontSize: 14,
        fontWeight: 'bold',
    },
    bottomSheetContent: {
        flex: 1,
        padding: 8,
    },
    pointNavigation: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: 16,
        paddingHorizontal: 8,
    },
    navButton: {
        paddingVertical: 8,
        paddingHorizontal: 16,
        borderRadius: 4,
        minWidth: 100,
        alignItems: 'center',
    },
    navButtonText: {
        color: 'white',
        fontWeight: '500',
    },
});

export default MultiMapScreen; 
import React from 'react';
import { Image, ImageSourcePropType, ImageStyle, StyleProp } from 'react-native';
import { ICON_SIZE } from '@/constants/UI/themes';
interface CIconProps {
    source: ImageSourcePropType | any;
    size?: number;
    style?: StyleProp<ImageStyle>;
    tintColor?: string;
}

const CIcon: React.FC<CIconProps> = ({ source, size = ICON_SIZE, style, tintColor }) => {
    return (
        <Image
            source={source}
            style={[
                {
                    width: size,
                    height: size,
                    resizeMode: 'contain',
                    tintColor,
                },
                style,
            ]}
        />
    );
};

export default CIcon;

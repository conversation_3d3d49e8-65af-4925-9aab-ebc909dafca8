import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { Animated, StyleSheet, View, useWindowDimensions } from 'react-native';
import { useTheme } from '@react-navigation/native';
import CText from '@/components/CText';
import { ToastContextType, ToastStatus } from '@/types';
import { FONT_WEIGHT, ZINDEX, FONT_FAMILY, COLORS, DESIGN_BASE_WIDTH } from '@/constants/UI/themes';
import { useTranslation } from 'react-i18next';
import { ItoastSuccess } from '@/constants/UI/icons';
import CIcon from '@/components/CIcon';
import { ItoastError } from '@/constants/UI/icons';


const ToastContext = createContext<ToastContextType | undefined>(undefined);

export const ToastProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const { dark } = useTheme();
    const colors = dark ? COLORS.dark : COLORS.light;
    const { width: windowWidth } = useWindowDimensions();
    const { t } = useTranslation();


    const toastWidth = Math.min(400, windowWidth - 10); // Max 400 or screen width minus margins

    const [message, setMessage] = useState<string | null>(null);
    const [type, setType] = useState<ToastStatus>('error');
    const [fadeAnim] = useState(new Animated.Value(0));
    const [pendingToast, setPendingToast] = useState<{ msg: string; toastType: ToastStatus } | null>(null);

    // Defer state updates using useEffect
    useEffect(() => {
        if (pendingToast) {
            setMessage(pendingToast.msg);
            setType(pendingToast.toastType);

            Animated.sequence([
                Animated.timing(fadeAnim, {
                    toValue: 1,
                    duration: 300,
                    useNativeDriver: true,
                }),
                Animated.delay(3000),
                Animated.timing(fadeAnim, {
                    toValue: 0,
                    duration: 300,
                    useNativeDriver: true,
                }),
            ]).start(() => {
                setMessage(null);
                setPendingToast(null);
            });
        }
    }, [pendingToast, fadeAnim]);

    const showToast = useCallback((msg: string, toastType: ToastStatus = 'error') => {
        // Instead of setting state directly, queue the toast update
        // Check if this is a translation key and translate it
        const translatedMessage = msg.includes('.') ? t(msg) : msg;
        setPendingToast({ msg: translatedMessage, toastType });
    }, [t]);

    return (
        <ToastContext.Provider value={{ showToast }}>
            {children}
            {message && (
                <Animated.View
                    style={[
                        styles.toast,
                        {
                            opacity: fadeAnim,
                            backgroundColor: colors.white,
                            width: toastWidth,
                        },
                    ]}
                >
                    <View
                        style={[
                            styles.icon,
                            {
                                backgroundColor: type === 'error' ? colors.error : colors.success
                            },
                        ]}
                    >
                        <CText style={{
                            ...styles.iconText,
                            color: colors.white,
                            fontWeight: FONT_WEIGHT.semiBold
                        }}>
                            {type === 'error' ? <CIcon source={ItoastError} size={24} tintColor={COLORS.light.white} /> : <CIcon source={ItoastSuccess} size={24} tintColor={COLORS.light.white} />}
                        </CText>
                    </View>
                    <CText style={[
                        styles.message,
                        {
                            color: type === 'error' ? colors.error : colors.success,
                            fontWeight: FONT_WEIGHT.semiBold
                        }
                    ]}>
                        {message}
                    </CText>
                </Animated.View>
            )}
        </ToastContext.Provider>
    );
};

export const useToast = () => {
    const context = useContext(ToastContext);
    if (!context) {
        throw new Error('useToast must be used within a ToastProvider');
    }
    return context;
};

const styles = StyleSheet.create({
    toast: {
        position: 'absolute',
        top: 40,
        left: 5,
        height: 66,
        flexDirection: 'row',
        alignItems: 'center',
        paddingTop: 14,
        paddingRight: 17,
        paddingBottom: 14,
        paddingLeft: 17,
        borderRadius: 10,
        zIndex: ZINDEX.toast,
        gap: 10,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    icon: {
        width: 24,
        height: 24,
        borderRadius: 12,
        justifyContent: 'center',
        alignItems: 'center',
    },
    iconText: {
        fontSize: 14,
        fontWeight: '600',
    },
    message: {
        flex: 1,
        fontSize: 18,
        lineHeight: 18,
        fontFamily: FONT_FAMILY.regular,
        fontWeight: '600',
    },
});
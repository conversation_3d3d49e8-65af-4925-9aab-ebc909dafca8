import { useCallback, useEffect, useState, useRef } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import PermissionsUtils, { PermissionType } from '../utils/permissions';

export type PermissionStatus = Record<PermissionType, boolean>;

/**
 * Hook to handle app permissions
 * @param initialPermissions Array of permissions to check and request
 * @returns Methods and state for permission handling
 */
export const useAppPermissions = (initialPermissions: PermissionType[] = []) => {
    const [permissionStatus, setPermissionStatus] = useState<PermissionStatus>({} as PermissionStatus);
    const [isChecking, setIsChecking] = useState(false);

    // Use a ref to track if initial check has been performed
    const initialCheckDone = useRef(false);
    // Track previous permission status to avoid unnecessary updates
    const prevStatusRef = useRef<PermissionStatus>({} as PermissionStatus);

    // Compare permission status objects to see if they've actually changed
    const hasStatusChanged = (oldStatus: PermissionStatus, newStatus: PermissionStatus): boolean => {
        const allKeys = new Set([...Object.keys(oldStatus), ...Object.keys(newStatus)]);
        for (const key of allKeys) {
            if (oldStatus[key as PermissionType] !== newStatus[key as PermissionType]) {
                return true;
            }
        }
        return false;
    };

    // Function to check all specified permissions
    const checkPermissions = useCallback(async (permissions: PermissionType[] = initialPermissions) => {
        if (permissions.length === 0) return {} as PermissionStatus;

        // Avoid setting isChecking if we already have the permissions cached
        const shouldCheckPermissions = !initialCheckDone.current ||
            permissions.some(p => typeof prevStatusRef.current[p] === 'undefined');

        if (shouldCheckPermissions) {
            setIsChecking(true);
        }

        try {
            const status = await PermissionsUtils.checkPermissions(permissions);

            // Only update state if the status actually changed
            if (hasStatusChanged(prevStatusRef.current, status)) {
                setPermissionStatus(prev => {
                    const newStatus = { ...prev, ...status };
                    prevStatusRef.current = newStatus;
                    return newStatus;
                });
            }

            initialCheckDone.current = true;
            return status;
        } catch (error) {
            console.error('Error checking permissions:', error);
            return {} as PermissionStatus;
        } finally {
            if (shouldCheckPermissions) {
                setIsChecking(false);
            }
        }
    }, [initialPermissions]);

    // Request a single permission
    const requestPermission = useCallback(async (
        permissionType: PermissionType,
        options: {
            showAlert?: boolean;
            alertTitle?: string;
            alertMessage?: string;
        } = {}
    ) => {
        setIsChecking(true);
        try {
            const granted = await PermissionsUtils.checkAndRequestPermission(permissionType, options);
            setPermissionStatus(prev => {
                const newStatus = { ...prev, [permissionType]: granted };
                prevStatusRef.current = newStatus;
                return newStatus;
            });
            return granted;
        } catch (error) {
            console.error(`Error requesting ${permissionType} permission:`, error);
            return false;
        } finally {
            setIsChecking(false);
        }
    }, []);

    // Request multiple permissions at once
    const requestPermissions = useCallback(async (
        permissionTypes: PermissionType[] = initialPermissions,
        showAlerts = true
    ) => {
        if (permissionTypes.length === 0) return {} as PermissionStatus;

        setIsChecking(true);
        try {
            const results = await PermissionsUtils.requestPermissions(permissionTypes);
            setPermissionStatus(prev => {
                const newStatus = { ...prev, ...results };
                prevStatusRef.current = newStatus;
                return newStatus;
            });
            return results;
        } catch (error) {
            console.error('Error requesting permissions:', error);
            return {} as PermissionStatus;
        } finally {
            setIsChecking(false);
        }
    }, [initialPermissions]);

    // Check if we have all required permissions
    const hasAllPermissions = useCallback((permissions: PermissionType[] = initialPermissions) => {
        return permissions.every(permission => permissionStatus[permission] === true);
    }, [permissionStatus, initialPermissions]);

    // Show settings alert for specific permission
    const showPermissionSettings = useCallback((
        title?: string,
        message?: string
    ) => {
        PermissionsUtils.showPermissionDeniedAlert(title, message);
    }, []);

    // Whenever app comes back to foreground, recheck permissions
    useEffect(() => {
        // Skip initial check - we'll handle it separately to avoid duplicate checks
        let didInitialCheck = false;

        const subscription = AppState.addEventListener('change', (nextAppState: AppStateStatus) => {
            if (nextAppState === 'active' && didInitialCheck) {
                // Only check permissions when returning to active state after initial check
                checkPermissions();
            }
        });

        // Initial check
        checkPermissions().then(() => {
            didInitialCheck = true;
        });

        return () => {
            subscription.remove();
        };
    }, [checkPermissions]);

    return {
        permissionStatus,
        isChecking,
        checkPermissions,
        requestPermission,
        requestPermissions,
        hasAllPermissions,
        showPermissionSettings,
    };
};

export default useAppPermissions; 
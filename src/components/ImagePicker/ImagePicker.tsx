import React, { useState, useCallback, useRef } from 'react';
import {
    View,
    StyleSheet,
    TouchableOpacity,
    Image,
    Platform,
    Alert,
    FlatList,
    Modal,
    SafeAreaView
} from 'react-native';
import { CameraRoll } from '@react-native-camera-roll/camera-roll';
import { useTheme } from '@react-navigation/native';
import { Camera, useCameraDevice, useCameraPermission } from 'react-native-vision-camera';
import CText from '@/components/CText';
import { FONT_WEIGHT } from '@/constants/UI/themes';
import { usePermission } from '@/hooks/usePermission';
import PhotoLibraryModal from './PhotoLibraryModal';

interface ImagePickerProps {
    multiple?: boolean;
    maxImages?: number;
    onImagesSelected: (images: string[]) => void;
    onCancel?: () => void;
}

interface GalleryImage {
    uri: string;
    selected: boolean;
}

const ImagePicker: React.FC<ImagePickerProps> = ({
    multiple = false,
    maxImages = 5,
    onImagesSelected,
    onCancel,
}) => {
    const { colors } = useTheme() as any;
    const [modalVisible, setModalVisible] = useState(false);
    const [galleryImages, setGalleryImages] = useState<GalleryImage[]>([]);
    const [cameraVisible, setCameraVisible] = useState(false);
    const device = useCameraDevice('back');
    const cameraRef = useRef<Camera>(null);
    const { hasPermission, requestPermission } = useCameraPermission();
    const [showLibrary, setShowLibrary] = useState(false);

    // Use the usePermission hook to handle photo library permissions
    const {
        requestMediaPermissions,
        requestBothStoragePermissions,
        request: requestPhotoLibraryPermission
    } = usePermission('photoLibrary');

    const openPicker = useCallback(async () => {
        let granted = false;

        // Check photo library permission
        if (Platform.OS === 'ios') {
            // Use the hook request instead of calling directly
            granted = await requestPhotoLibraryPermission();
        } else {
            // On Android, check the version to handle appropriately
            if (Number(Platform.Version) >= 33) {
                // Android 13+ uses new media permissions
                granted = await requestMediaPermissions?.() || false;
            } else {
                // Older Android uses READ_EXTERNAL_STORAGE permission
                granted = await requestBothStoragePermissions?.() || false;
            }
        }

        if (!granted) {
            Alert.alert(
                'Permission Required',
                'The app needs photo library access to select images',
                [{ text: 'OK' }]
            );
            return;
        }

        try {
            const photos = await CameraRoll.getPhotos({
                first: 50,
                assetType: 'Photos',
            });

            const formattedPhotos = photos.edges.map(edge => ({
                uri: edge.node.image.uri,
                selected: false,
            }));

            setGalleryImages(formattedPhotos);
            setModalVisible(true);
        } catch (error) {
            console.error('Error loading photos:', error);
            Alert.alert('Error', 'Unable to load photos from library');
        }
    }, [requestPhotoLibraryPermission, requestMediaPermissions, requestBothStoragePermissions]);

    const openCamera = useCallback(async () => {
        if (!hasPermission) {
            const granted = await requestPermission();
            if (!granted) {
                Alert.alert(
                    'Permission Required',
                    'The app needs camera access to take photos',
                    [{ text: 'OK' }]
                );
                return;
            }
        }

        setCameraVisible(true);
    }, [hasPermission, requestPermission]);

    const takePicture = useCallback(async () => {
        if (cameraRef.current) {
            try {
                const photo = await cameraRef.current.takePhoto({
                    flash: 'off',
                });

                // Create URI for the captured photo
                const uri = Platform.OS === 'ios'
                    ? `file://${photo.path}`
                    : photo.path;

                // Close camera and return the captured photo
                setCameraVisible(false);
                onImagesSelected([uri]);
            } catch (error) {
                console.error('Error taking photo:', error);
                Alert.alert('Error', 'Unable to take photo');
            }
        }
    }, [onImagesSelected]);

    const toggleImageSelection = useCallback((index: number) => {
        setGalleryImages(prev => {
            const updatedImages = [...prev];

            if (!multiple) {
                // If not selecting multiple images, deselect all other images
                updatedImages.forEach((img, i) => {
                    updatedImages[i].selected = i === index;
                });
            } else {
                // If selecting multiple images
                const selectedCount = updatedImages.filter(img => img.selected).length;

                // If image is already selected, deselect it
                if (updatedImages[index].selected) {
                    updatedImages[index].selected = false;
                }
                // If image is not selected and haven't reached the limit, select it
                else if (selectedCount < maxImages) {
                    updatedImages[index].selected = true;
                }
                // If reached the limit, show notification
                else {
                    Alert.alert('Notice', `You can only select up to ${maxImages} images`);
                }
            }

            return updatedImages;
        });
    }, [multiple, maxImages]);

    const confirmSelection = useCallback(() => {
        const selectedImages = galleryImages
            .filter(img => img.selected)
            .map(img => img.uri);

        if (selectedImages.length === 0) {
            Alert.alert('Notice', 'Please select at least one image');
            return;
        }

        onImagesSelected(selectedImages);
        setModalVisible(false);
    }, [galleryImages, onImagesSelected]);

    const renderGalleryItem = useCallback(({ item, index }: { item: GalleryImage; index: number }) => (
        <TouchableOpacity
            style={[
                styles.imageItem,
                item.selected && { borderColor: colors.primary, borderWidth: 2 }
            ]}
            onPress={() => toggleImageSelection(index)}
        >
            <Image source={{ uri: item.uri }} style={styles.thumbnail} />
            {item.selected && (
                <View style={[styles.selectedIndicator, { backgroundColor: colors.primary }]}>
                    <CText style={styles.selectedText}>✓</CText>
                </View>
            )}
        </TouchableOpacity>
    ), [colors.primary, toggleImageSelection]);

    const cancelSelection = useCallback(() => {
        setModalVisible(false);
        if (onCancel) onCancel();
    }, [onCancel]);

    const handleImagesSelected = (images: any) => {
        console.log('Selected images:', images);
        // Xử lý ảnh đã chọn
    };

    return (
        <View>
            <View style={styles.buttonContainer}>
                <TouchableOpacity
                    style={[styles.button, { backgroundColor: colors.primary }]}
                    onPress={openPicker}
                >
                    <CText style={[styles.buttonText, { color: colors.background }]}>
                        Choose from Library
                    </CText>
                </TouchableOpacity>

                <TouchableOpacity
                    style={[styles.button, { backgroundColor: colors.background, borderColor: colors.primary, borderWidth: 1 }]}
                    onPress={openCamera}
                >
                    <CText style={[styles.buttonText, { color: colors.primary }]}>
                        Take New Photo
                    </CText>
                </TouchableOpacity>
            </View>

            {/* Modal to display photo library */}
            <PhotoLibraryModal
                visible={showLibrary}
                onClose={() => setShowLibrary(false)}
                onSelect={handleImagesSelected}
                multiple={true}
                maxImages={3}
            />

            {/* Camera modal */}
            {device && (
                <Modal
                    visible={cameraVisible}
                    animationType="slide"
                    onRequestClose={() => setCameraVisible(false)}
                >
                    <SafeAreaView style={styles.cameraContainer}>
                        <Camera
                            ref={cameraRef}
                            style={StyleSheet.absoluteFill}
                            device={device}
                            isActive={cameraVisible}
                            photo={true}
                        />
                        <View style={styles.cameraControls}>
                            <TouchableOpacity
                                style={styles.closeButton}
                                onPress={() => setCameraVisible(false)}
                            >
                                <CText style={styles.closeButtonText}>✕</CText>
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={styles.captureButton}
                                onPress={takePicture}
                            >
                                <View style={styles.captureButtonInner} />
                            </TouchableOpacity>
                        </View>
                    </SafeAreaView>
                </Modal>
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    buttonContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 16,
    },
    button: {
        flex: 1,
        paddingVertical: 12,
        borderRadius: 8,
        alignItems: 'center',
        justifyContent: 'center',
        marginHorizontal: 5,
    },
    buttonText: {
        fontSize: 16,
        fontWeight: FONT_WEIGHT.medium,
    },
    modalContainer: {
        flex: 1,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#e0e0e0',
    },
    headerButton: {
        fontSize: 16,
        fontWeight: FONT_WEIGHT.medium,
    },
    headerTitle: {
        fontSize: 16,
        fontWeight: FONT_WEIGHT.semiBold,
    },
    imageGrid: {
        padding: 4,
    },
    imageItem: {
        flex: 1,
        margin: 4,
        aspectRatio: 1,
        borderRadius: 8,
        overflow: 'hidden',
    },
    thumbnail: {
        width: '100%',
        height: '100%',
    },
    selectedIndicator: {
        position: 'absolute',
        top: 8,
        right: 8,
        width: 24,
        height: 24,
        borderRadius: 12,
        alignItems: 'center',
        justifyContent: 'center',
    },
    selectedText: {
        color: '#ffffff',
        fontWeight: FONT_WEIGHT.bold,
    },
    cameraContainer: {
        flex: 1,
        backgroundColor: 'black',
    },
    cameraControls: {
        position: 'absolute',
        bottom: 40,
        left: 0,
        right: 0,
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
    },
    closeButton: {
        position: 'absolute',
        top: 16,
        left: 16,
        width: 40,
        height: 40,
        borderRadius: 20,
        backgroundColor: 'rgba(0,0,0,0.5)',
        alignItems: 'center',
        justifyContent: 'center',
    },
    closeButtonText: {
        color: 'white',
        fontSize: 18,
        fontWeight: FONT_WEIGHT.bold,
    },
    captureButton: {
        width: 70,
        height: 70,
        borderRadius: 35,
        backgroundColor: 'rgba(255,255,255,0.3)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    captureButtonInner: {
        width: 60,
        height: 60,
        borderRadius: 30,
        backgroundColor: 'white',
    },
});

export default ImagePicker; 
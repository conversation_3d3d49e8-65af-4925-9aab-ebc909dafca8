import { createSlice } from '@reduxjs/toolkit';

interface LoadingState {
    isLoading: boolean;
    loadingCount: number; // Thêm counter để xử lý nhiều loading cùng lúc
}

const initialState: LoadingState = {
    isLoading: false,
    loadingCount: 0,
};

const loadingSlice = createSlice({
    name: 'loading',
    initialState,
    reducers: {
        showLoading: (state) => {
            state.loadingCount += 1;
            state.isLoading = true;
            console.log('Show Loading, count:', state.loadingCount); // Debug
        },
        hideLoading: (state) => {
            state.loadingCount = Math.max(0, state.loadingCount - 1);
            state.isLoading = state.loadingCount > 0;
            console.log('Hide Loading, count:', state.loadingCount); // Debug
        },
    },
});

export const { showLoading, hideLoading } = loadingSlice.actions;
export default loadingSlice.reducer;
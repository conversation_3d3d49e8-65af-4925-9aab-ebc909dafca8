/**
 * Utilities related to maps and location
 */

/**
 * Convert distance from meters to display text
 * @param distance Distance in meters
 * @returns Formatted distance string
 */
export const getDistanceText = (distance: number): string => {
    if (distance < 1000) {
        return `${Math.round(distance)} m`;
    } else {
        return `${(distance / 1000).toFixed(1)} km`;
    }
};

/**
 * Convert time from seconds to display text
 * @param seconds Time in seconds
 * @returns Formatted time string
 */
export const getEstimatedTimeText = (seconds: number): string => {
    if (seconds < 60) {
        return `${Math.round(seconds)} seconds`;
    } else if (seconds < 3600) {
        const minutes = Math.floor(seconds / 60);
        return `${minutes} minutes`;
    } else {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        return `${hours} hours ${minutes} minutes`;
    }
};

/**
 * Calculate center point for a set of map points
 * @param points Array of points with latitude and longitude
 * @returns Center point and appropriate zoom level
 */
export const calculateMapCenter = (
    points: Array<{ latitude: number; longitude: number }>
) => {
    if (points.length === 0) {
        // Default to Ho Chi Minh City
        return {
            latitude: 10.762622,
            longitude: 106.660172,
            latitudeDelta: 0.0922,
            longitudeDelta: 0.0421,
        };
    }

    if (points.length === 1) {
        return {
            latitude: points[0].latitude,
            longitude: points[0].longitude,
            latitudeDelta: 0.01,
            longitudeDelta: 0.01,
        };
    }

    // Calculate min/max for coordinates
    let minLat = points[0].latitude;
    let maxLat = points[0].latitude;
    let minLng = points[0].longitude;
    let maxLng = points[0].longitude;

    points.forEach(point => {
        minLat = Math.min(minLat, point.latitude);
        maxLat = Math.max(maxLat, point.latitude);
        minLng = Math.min(minLng, point.longitude);
        maxLng = Math.max(maxLng, point.longitude);
    });

    // Calculate center
    const centerLat = (minLat + maxLat) / 2;
    const centerLng = (minLng + maxLng) / 2;

    // Calculate delta (zoom level) with 50% padding
    const latDelta = (maxLat - minLat) * 1.5;
    const lngDelta = (maxLng - minLng) * 1.5;

    return {
        latitude: centerLat,
        longitude: centerLng,
        latitudeDelta: Math.max(0.01, latDelta),
        longitudeDelta: Math.max(0.01, lngDelta),
    };
};

/**
 * Sort points by distance from a starting point
 * @param points Array of points
 * @param startPoint Starting point
 * @returns Array of sorted points
 */
export const sortPointsByDistance = (
    points: Array<{ latitude: number; longitude: number;[key: string]: any }>,
    startPoint: { latitude: number; longitude: number }
): typeof points => {
    if (points.length <= 1) return [...points];

    // Sort by distance from starting point
    return [...points].sort((a, b) => {
        const distA = calculateDistance(startPoint, a);
        const distB = calculateDistance(startPoint, b);
        return distA - distB;
    });
};

/**
 * Calculate distance between two points using Haversine formula
 * @param point1 First point
 * @param point2 Second point
 * @returns Distance in meters
 */
export const calculateDistance = (
    point1: { latitude: number; longitude: number },
    point2: { latitude: number; longitude: number }
): number => {
    const R = 6371e3; // Earth radius in meters
    const φ1 = (point1.latitude * Math.PI) / 180;
    const φ2 = (point2.latitude * Math.PI) / 180;
    const Δφ = ((point2.latitude - point1.latitude) * Math.PI) / 180;
    const Δλ = ((point2.longitude - point1.longitude) * Math.PI) / 180;

    const a =
        Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
        Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c; // Distance in meters
};

/**
 * Check if GPS is enabled
 * Note: Needs to be implemented per platform
 */
export const isLocationEnabled = async (): Promise<boolean> => {
    // Implement according to specific platform
    // On Android: LocationManager, on iOS: CLLocationManager
    return true; // Assume enabled
};

/**
 * Request user to enable GPS
 * Note: Needs to be implemented per platform
 */
export const requestLocationEnable = async (): Promise<boolean> => {
    // Implement according to specific platform
    return true; // Assume success
}; 
import { ScreenConfig } from "@/types";
import DashboardScreen from "@/screens/DashboardScreen";
import DemoScreen from "@/screens/DemoScreen";
import LoginScreen from "@/screens/LoginScreen";
import ForgotPasswordScreen from "@/screens/ForgotPasswordScreen";
import i18n from "@/i18n/i18n";
import TaskDetailScreen from "@/screens/TaskDetailScreen";
import SettingScreen from "@/screens/SettingScreen";
import ProfileScreen from "@/screens/ProfileScreen";
import ChangePasswordScreen from "@/screens/ChangePasswordScreen";
import MapDemoScreen from "@/screens/Map/MapDemoScreen";
import GeneralSettingScreen from "@/screens/GeneralSettingScreen";
import { getResponsiveFontSize } from "./UI/responsive";
import { FONT_SIZE } from "./UI/themes";

// Screen names constants
export const SCREEN_NAMES = {
    HOME: "Home",
    DEMO: "Demo",
    LOGIN: "Login",
    FORGOT_PASSWORD: "ForgotPassword",
    DETAIL_TASK: "DetailTask",
    SETTINGS: "Settings",
    PROFILE: "Profile",
    CHANGE_PASSWORD: "ChangePassword",
    MAP_DEMO: "MapDemoScreen",
    GENERAL_SETTING: "GeneralSetting",
};

// Navigator types
export const NAVIGATOR_TYPES = {
    DRAWER: "drawer",
    STACK: "stack",
    TAB: "tab"
};

// Screen grouping by navigator type
export const DRAWER_SCREENS = [SCREEN_NAMES.HOME, SCREEN_NAMES.DEMO, SCREEN_NAMES.SETTINGS, SCREEN_NAMES.DETAIL_TASK, SCREEN_NAMES.PROFILE, SCREEN_NAMES.CHANGE_PASSWORD, SCREEN_NAMES.MAP_DEMO, SCREEN_NAMES.GENERAL_SETTING];
export const STACK_SCREENS = [SCREEN_NAMES.LOGIN, SCREEN_NAMES.FORGOT_PASSWORD,];

// Get translated screen title
export const getScreenTitle = (key: string) => {
    switch (key) {
        case SCREEN_NAMES.HOME:
            return i18n.t('screens.dashboard');
        case SCREEN_NAMES.DEMO:
            return i18n.t('screens.demo');
        case SCREEN_NAMES.LOGIN:
            return i18n.t('screens.login');
        case SCREEN_NAMES.FORGOT_PASSWORD:
            return i18n.t('screens.forgotPassword');
        case SCREEN_NAMES.DETAIL_TASK:
            return i18n.t('screens.detailTask');
        case SCREEN_NAMES.SETTINGS:
            return i18n.t('screens.settings');
        case SCREEN_NAMES.PROFILE:
            return i18n.t('screens.profile');
        case SCREEN_NAMES.CHANGE_PASSWORD:
            return i18n.t('screens.changePassword.title');
        case SCREEN_NAMES.MAP_DEMO:
            return i18n.t('screens.mapDemo');
        case SCREEN_NAMES.GENERAL_SETTING:
            return i18n.t('screens.generalSetting');

        default:
            return key;
    }
};


const headerOptions = {
    headerTitleAlign: 'center', // Căn giữa title
    headerTitleStyle: {
        fontFamily: 'Lato-Regular',
        fontWeight: '600',
        fontSize: getResponsiveFontSize(FONT_SIZE.size26),
    },
}

// Screen configurations for reuse
export const getScreenConfigs = (): Record<string, ScreenConfig> => ({
    [SCREEN_NAMES.HOME]: {
        name: SCREEN_NAMES.HOME,
        component: DashboardScreen,
        options: {
            title: getScreenTitle(SCREEN_NAMES.HOME),
            drawerLabel: getScreenTitle(SCREEN_NAMES.HOME)
        }
    },
    [SCREEN_NAMES.DEMO]: {
        name: SCREEN_NAMES.DEMO,
        component: DemoScreen,
        options: {
            title: getScreenTitle(SCREEN_NAMES.DEMO),
            drawerLabel: getScreenTitle(SCREEN_NAMES.DEMO)
        }
    },
    [SCREEN_NAMES.LOGIN]: {
        name: SCREEN_NAMES.LOGIN,
        component: LoginScreen,
        options: {
            title: getScreenTitle(SCREEN_NAMES.LOGIN),

        }
    },
    [SCREEN_NAMES.FORGOT_PASSWORD]: {
        name: SCREEN_NAMES.FORGOT_PASSWORD,
        component: ForgotPasswordScreen,
        options: {
            title: getScreenTitle(SCREEN_NAMES.FORGOT_PASSWORD),
            ...headerOptions as any,

        },
    },
    [SCREEN_NAMES.DETAIL_TASK]: {
        name: SCREEN_NAMES.DETAIL_TASK,
        component: TaskDetailScreen,
        options: {
            title: getScreenTitle(SCREEN_NAMES.DETAIL_TASK),
            ...headerOptions as any

        }
    },
    [SCREEN_NAMES.SETTINGS]: {
        name: SCREEN_NAMES.SETTINGS,
        component: SettingScreen,
        options: {
            title: getScreenTitle(SCREEN_NAMES.SETTINGS),
            drawerLabel: getScreenTitle(SCREEN_NAMES.SETTINGS),
            ...headerOptions as any
        }
    },
    [SCREEN_NAMES.PROFILE]: {
        name: SCREEN_NAMES.PROFILE,
        component: ProfileScreen,
        options: {
            title: getScreenTitle(SCREEN_NAMES.PROFILE),
            drawerLabel: () => null,
            drawerItemStyle: { display: 'none' },
            ...headerOptions as any
        }
    },
    [SCREEN_NAMES.CHANGE_PASSWORD]: {
        name: SCREEN_NAMES.CHANGE_PASSWORD,
        component: ChangePasswordScreen,
        options: {
            title: getScreenTitle(SCREEN_NAMES.CHANGE_PASSWORD),
            drawerLabel: () => null,
            drawerItemStyle: { display: 'none' },
            ...headerOptions as any
        }
    },
    [SCREEN_NAMES.MAP_DEMO]: {
        name: SCREEN_NAMES.MAP_DEMO,
        component: MapDemoScreen,
        options: {
            title: getScreenTitle(SCREEN_NAMES.MAP_DEMO),
            drawerLabel: () => null,
            drawerItemStyle: { display: 'none' },
            ...headerOptions as any

        }
    },
    [SCREEN_NAMES.GENERAL_SETTING]: {
        name: SCREEN_NAMES.GENERAL_SETTING,
        component: GeneralSettingScreen,
        options: {
            title: getScreenTitle(SCREEN_NAMES.GENERAL_SETTING),
            drawerLabel: () => null,
            drawerItemStyle: { display: 'none' },
            ...headerOptions as any

        }
    },
});

// Helper function to get screen configs by navigator type
export const getScreenConfigsByType = (navigatorType: string): ScreenConfig[] => {
    const screenConfigs = getScreenConfigs();

    switch (navigatorType) {
        case NAVIGATOR_TYPES.DRAWER:
            return DRAWER_SCREENS.map(screenName => screenConfigs[screenName]);
        case NAVIGATOR_TYPES.STACK:
            return STACK_SCREENS.map(screenName => screenConfigs[screenName]);
        default:
            return [];
    }
};

export const PRIVACY_POLICY_URL = "https://www.avbmarketing.com/privacy-policy";
import React, { useCallback, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import useAppPermissions from '../hooks/useAppPermissions';
import { PermissionType } from '../utils/permissions';

interface PermissionsHandlerProps {
    children: React.ReactNode;
    requiredPermissions?: PermissionType[];
    optionalPermissions?: PermissionType[];
    autoRequestRequired?: boolean;
    autoRequestOptional?: boolean;
    onPermissionsChecked?: (status: Record<PermissionType, boolean>) => void;
    onPermissionsGranted?: () => void;

}

const PERMISSIONS_REQUESTED_ONCE_KEY = 'permissions_requested_once';

const PermissionsHandler: React.FC<PermissionsHandlerProps> = ({
    children,
    requiredPermissions = [],
    optionalPermissions = [],
    autoRequestRequired = true,
    autoRequestOptional = false,
    onPermissionsChecked,
    onPermissionsGranted,
}) => {
    const allPermissions = [...requiredPermissions, ...optionalPermissions];
    const { requestPermissions, checkPermissions } = useAppPermissions(allPermissions);

    const checkAllPermissions = useCallback(async () => {
        const requestedOnce = await AsyncStorage.getItem(PERMISSIONS_REQUESTED_ONCE_KEY);
        const status = await checkPermissions(allPermissions);
        onPermissionsChecked?.(status);

        if (!requestedOnce && autoRequestRequired && requiredPermissions.length > 0) {
            const missingRequired = requiredPermissions.filter(
                permission => !status[permission]
            );
            if (missingRequired.length > 0) {
                await requestPermissions(missingRequired);
            }
        }

        if (!requestedOnce && autoRequestOptional && optionalPermissions.length > 0) {
            const missingOptional = optionalPermissions.filter(
                permission => !status[permission]
            );
            if (missingOptional.length > 0) {
                await requestPermissions(missingOptional);
            }
        }

        if (!requestedOnce && (autoRequestRequired || autoRequestOptional)) {
            await AsyncStorage.setItem(PERMISSIONS_REQUESTED_ONCE_KEY, 'true');
        }

        onPermissionsGranted?.();

    }, [checkPermissions, allPermissions, requiredPermissions, optionalPermissions, autoRequestRequired, autoRequestOptional, requestPermissions, onPermissionsChecked, onPermissionsGranted]);

    useEffect(() => {
        checkAllPermissions();
    }, [checkAllPermissions]);

    return <>{children}</>;
};

export default PermissionsHandler;
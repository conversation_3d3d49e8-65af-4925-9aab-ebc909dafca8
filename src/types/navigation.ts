import { DrawerNavigationOptions } from "@react-navigation/drawer";
import { NativeStackNavigationOptions } from "@react-navigation/native-stack";
import { Task } from "@/interfaces/task";

export type DrawerParamList = {
    Home: undefined;
    Login: undefined;
    Dashboard: undefined;
    ReportedAndHistory: undefined;
    Schedule: undefined;
    StockInventory: undefined;
    PriceList: undefined;
    Setting: undefined;
    TaskDetail: { task: Task };
    ImageResize: undefined;
};

export type StackParamList = {
    Login: undefined;
    ForgotPassword: undefined;
};

export interface ScreenConfig {
    name: string;
    component: React.ComponentType<any>;
    options: DrawerNavigationOptions | NativeStackNavigationOptions;
}
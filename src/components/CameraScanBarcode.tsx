import React from 'react';
import CameraBase from './CameraBase';
import { CodeType } from 'react-native-vision-camera';

interface CameraScanBarcodeProps {
    onCodeScanned?: (code: string) => void;
    onClose?: () => void;
    isScanMode?: boolean;
    onPhotoTaken?: (uri: string) => void;
    initialCameraPosition?: 'back' | 'front';
    initialFlashMode?: 'auto' | 'on' | 'off';
    customCodeTypes?: CodeType[];
}

const BARCODE_TYPES: CodeType[] = ['qr', 'ean-13', 'ean-8', 'code-128', 'code-39', 'code-93', 'codabar', 'itf', 'upc-e'];

const CameraScanBarcode: React.FC<CameraScanBarcodeProps> = ({
    onCodeScanned,
    onClose,
    isScanMode = true,
    onPhotoTaken,
    initialCameraPosition = 'back',
    initialFlashMode = 'auto',
    customCodeTypes
}) => {
    return (
        <CameraBase
            onCodeScanned={onCodeScanned}
            onClose={onClose}
            isScanMode={isScanMode}
            onPhotoTaken={onPhotoTaken}
            initialCameraPosition={initialCameraPosition}
            initialFlashMode={initialFlashMode}
            codeTypes={customCodeTypes || BARCODE_TYPES}
            showCloseButton={true}
        />
    );
};

export default CameraScanBarcode;
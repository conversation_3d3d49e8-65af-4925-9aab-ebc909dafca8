import React from 'react';
import {
    View,
    StyleSheet,
    TouchableOpacity,
    ScrollView,
    Alert
} from 'react-native';
import {
    DrawerContentScrollView,
    DrawerContentComponentProps,
} from '@react-navigation/drawer';
import { useTheme } from '@react-navigation/native';
import CText from './CText';
import CIcon from './CIcon';
import { useTranslation } from 'react-i18next';
import { useResponsiveUtils } from '@/constants/UI/responsive';
import { FONT_SIZE, SPACING, PADDING, FONT_WEIGHT, COLORS } from '@/constants/UI/themes';
import { Iback, Icustomer, Ihome, Ilogout, Isetting } from '@/constants/UI/icons';
import { BASE_STYLE } from '@/constants/UI/responsiveStyles';
import ProfileAvatar from './ProfileAvatar';
import LinearGradient from 'react-native-linear-gradient';
import { AppTheme } from '@/types';
import { useDispatch, useSelector } from 'react-redux';
import { logoutUser } from '@/redux/slices/authSlice';
import { AppDispatch, RootState } from '@/redux/store';
import { useToast } from '@/hocs/toast';

interface MenuItem {
    id: string;
    label: string;
    icon: string;
    isLogout?: boolean;
    isLanguage?: boolean;
}


const CustomDrawer = (props: DrawerContentComponentProps) => {
    const theme = useTheme() as unknown as AppTheme;
    const { colors } = theme;
    const { t } = useTranslation();
    const { scale, pixelSize, width, isLandscape } = useResponsiveUtils();
    const dispatch = useDispatch<AppDispatch>();
    const { data } = useSelector((state: RootState) => state.user);
    const { showToast } = useToast();
    const menuItems: MenuItem[] = [
        { label: t('menu.home'), icon: Ihome, id: 'Home' },
        // { label: t('menu.batch'), icon: Ibatch, id: 'Batch' },
        // { label: t('menu.report'), icon: Ireport, id: 'Report' },
        // { label: t('auth.forgotPassword'), icon: Icustomer, id: 'ForgotPassword' },
        // { label: t('auth.signin'), icon: Ipackage, id: 'Login' },
        { label: t('menu.settings'), icon: Isetting, id: 'Settings' },
        { label: t('menu.demo'), icon: Icustomer, id: 'Demo' },
        { label: t('auth.logout'), icon: Ilogout, isLogout: true, id: '' },
    ];

    const handleLogout = () => {
        Alert.alert(
            t('auth.logoutConfirmation'),
            '',
            [
                {
                    text: t('common.cancel'),
                    style: 'cancel'
                },
                {
                    text: t('common.logout'),
                    onPress: () => {
                        dispatch(logoutUser()).unwrap()
                            .then((result: any) => {
                                if (result.success) {
                                    showToast(result.data.message, 'success');
                                    props.navigation.closeDrawer();
                                }
                            })
                            .catch((error) => {
                                if (error && typeof error === 'object') {
                                    console.error('Logout failed:', error);
                                    Alert.alert(
                                        t('errors.logoutError'),
                                        t('errors.somethingWentWrong')
                                    );
                                }
                            });
                    }
                }
            ]
        );
    };

    const renderDrawerItem = ({ label, icon, isLogout, id }: MenuItem) => (
        <TouchableOpacity
            style={[
                styles.menuItem,
                isLogout && styles.logoutItem,
                { marginBottom: SPACING.xxl }
            ]}
            onPress={() => {
                if (isLogout) {
                    handleLogout();
                } else {
                    props.navigation.navigate(id);
                }
            }}
        >
            <CIcon
                source={icon}
                size={pixelSize(FONT_SIZE.size40)}
                style={styles.menuIcon}
            />
            <CText
                style={[
                    styles.menuText,
                    { color: isLogout ? colors.error : colors.primary },
                    { fontSize: pixelSize(FONT_SIZE.size20), fontWeight: FONT_WEIGHT.medium },
                ]}
            >
                {label}
            </CText>
        </TouchableOpacity>
    );

    return (
        <ScrollView style={[styles.container]}>
            {/* Header */}

            <LinearGradient
                colors={colors.backgroundGradient}
                locations={[0.1, 0.9]}
                style={styles.header}>
                <TouchableOpacity
                    onPress={() => props.navigation.closeDrawer()}
                    style={styles.backButton}
                >
                    <CIcon
                        source={Iback}
                        size={pixelSize(FONT_SIZE.size40)}
                        style={styles.backIcon}
                    />
                </TouchableOpacity>
                <View style={styles.headerContent}>
                    <View style={styles.userInfo}>
                        <ProfileAvatar
                            style={styles.avatar}
                            name={data?.name}
                        />
                        <View style={styles.userTextContainer}>
                            <CText
                                size="size26"
                                style={[
                                    styles.userName,
                                    {
                                        color: '#000',
                                        fontWeight: FONT_WEIGHT.medium
                                    }
                                ]}
                            >
                                {data?.name || 'User'}
                            </CText>
                            <CText
                                size="size20"
                                style={[
                                    styles.userEmail,
                                    {
                                        color: colors.text,
                                        fontWeight: FONT_WEIGHT.medium,
                                        opacity: 0.8
                                    }
                                ]}
                            >
                                {data?.job_title || 'User'}
                            </CText>
                        </View>
                    </View>
                </View>
            </LinearGradient>

            {/* Drawer Items */}
            <DrawerContentScrollView
                {...props}
                contentContainerStyle={[
                    styles.drawerContent,
                    {
                        backgroundColor: colors.background,
                        paddingTop: scale(SPACING.xxl, width),
                    },
                ]}
                style={styles.drawer}
            >
                {menuItems.map((item, index) => (
                    <View key={index}>{renderDrawerItem(item)}</View>
                ))}
            </DrawerContentScrollView>

        </ScrollView>

    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        position: 'relative',
    },
    header: {
        flexDirection: 'row',
        height: 221,
        position: 'relative',
    },
    backButton: {
        position: 'absolute',
        left: 30,
        top: 71,
        zIndex: 1,
    },
    backIcon: {
        padding: SPACING.xs,
    },
    headerContent: {
        flex: 1,
        ...BASE_STYLE.centerContent,
        paddingTop: 73,
        height: 221,
    },
    userInfo: {
        ...BASE_STYLE.centerContent,
        // width: 123,
        height: 128,
    },
    avatar: {
        marginBottom: SPACING.xs,
        borderWidth: 1,
        borderColor: COLORS.light.black,
    },
    userTextContainer: {
        ...BASE_STYLE.centerContent,
    },
    userName: {
        textAlign: 'center',
    },
    userEmail: {
        textAlign: 'center',
    },
    drawer: { paddingLeft: SPACING.lg },
    drawerContent: {
        flex: 1,
    },
    menuItem: {
        ...BASE_STYLE.rowCenter
    },
    menuIcon: {
        marginRight: SPACING.sm,
    },
    menuText: {},
    logoutItem: {},
    logoutLine: {
        position: 'absolute',
        bottom: 0,
        left: SPACING.md,
        right: SPACING.md,
        height: 2,
        backgroundColor: COLORS.light.error,
    },
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        ...BASE_STYLE.centerContent,
    },
    modalContent: {
        width: '80%',
        padding: PADDING.lg,
        borderRadius: 10,
        elevation: 5,
    },
    modalTitle: {
        textAlign: 'center',
        marginBottom: SPACING.lg,
        fontWeight: 'bold',
    },
    languageOption: {
        padding: PADDING.md,
        marginVertical: SPACING.xs,
        borderRadius: 5,
        backgroundColor: '#F5F5F5',
    },

    closeButton: {
        marginTop: SPACING.lg,
        padding: PADDING.sm,
        backgroundColor: '#E6E6E6',
        borderRadius: 5,
    },
    closeButtonText: {
        textAlign: 'center',
        color: '#333',
    },
});

export default CustomDrawer;
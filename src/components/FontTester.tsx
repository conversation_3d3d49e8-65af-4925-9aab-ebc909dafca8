import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { FONT_FAMILY, FONT_SIZE } from '../constants/UI/themes';

const FontTester = () => {
    const fontTests = Object.entries(FONT_FAMILY).map(([key, fontFamily]) => ({
        name: key,
        style: { fontFamily }
    }));

    return (
        <ScrollView style={styles.container}>
            <Text style={styles.title}>Font Tester</Text>
            {fontTests.map((font, index) => (
                <View key={index} style={styles.fontContainer}>
                    <Text style={styles.fontName}>{font.name}</Text>
                    <Text style={[styles.sampleText, font.style]}>
                        The quick brown fox jumps over the lazy dog
                    </Text>
                </View>
            ))}
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: 16,
    },
    title: {
        fontSize: FONT_SIZE.xl,
        fontFamily: FONT_FAMILY.bold,
        marginBottom: 20,
    },
    fontContainer: {
        marginBottom: 16,
        padding: 12,
        backgroundColor: '#f5f5f5',
        borderRadius: 8,
    },
    fontName: {
        fontSize: FONT_SIZE.sm,
        color: '#666',
        marginBottom: 4,
    },
    sampleText: {
        fontSize: FONT_SIZE.md,
    },
});

export default FontTester; 
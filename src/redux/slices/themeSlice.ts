
import { THEME } from '@/constants/UI/themes';
import { ThemeOptions } from '@/types';
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
export interface ThemeState {
    theme: ThemeOptions;
    loading: boolean;
    error: string | null;
}

const initialState: ThemeState = {
    theme: THEME.LIGHT,
    loading: false,
    error: null,
};

const themeSlice = createSlice({
    name: 'theme',
    initialState,
    reducers: {
        toggleTheme(state) {
            state.theme = state.theme === THEME.DARK ? THEME.LIGHT : THEME.DARK; // fix logic
        },
        setTheme(state, action: PayloadAction<ThemeOptions>) {
            state.theme = action.payload;
        },
        setLoading(state, action: PayloadAction<boolean>) {
            state.loading = action.payload;
        },
        setError(state, action: PayloadAction<string | null>) {
            state.error = action.payload;
        },
    },
});

export const { toggleTheme, setTheme, setLoading, setError } = themeSlice.actions;
export default themeSlice.reducer;

export const selectTheme = (state: { theme: ThemeState }) => state.theme.theme;
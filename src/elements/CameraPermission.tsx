import React, { useEffect, useState, useRef } from 'react';
import { View, Text, Button, Platform, Alert, StyleSheet, Linking, Image } from 'react-native';
import { check, request, PERMISSIONS, RESULTS } from 'react-native-permissions';
import { Camera, useCameraDevice } from 'react-native-vision-camera';

function CameraPermission() {
    const [permissionStatus, setPermissionStatus] = useState('Chưa kiểm tra');
    const [capturedPhoto, setCapturedPhoto] = useState<string | null>(null);
    const cameraRef = useRef<Camera>(null);

    // Lấy thiết bị camera (camera sau)
    const device = useCameraDevice('back');

    // Hàm kiểm tra trạng thái quyền camera
    const checkCameraPermission = () => {
        const permission =
            Platform.OS === 'ios'
                ? PERMISSIONS.IOS.CAMERA
                : PERMISSIONS.ANDROID.CAMERA;

        check(permission).then((status) => {
            console.log('Camera permission status :>> ', status);
            setPermissionStatus(status);

            if (status === 'denied') {
                Alert.alert(
                    'Quyền truy cập camera bị từ chối',
                    'Ứng dụng cần quyền truy cập camera để chụp ảnh hoặc quay video. Bạn có muốn yêu cầu lại quyền này không?',
                    [
                        { text: 'Hủy', style: 'cancel' },
                        { text: 'Yêu cầu lại', onPress: requestCameraPermission },
                    ]
                );
            } else if (status === 'blocked') {
                Alert.alert(
                    'Quyền truy cập camera bị chặn',
                    'Quyền truy cập camera đã bị chặn. Vui lòng vào cài đặt để cấp quyền.',
                    [
                        { text: 'Hủy', style: 'cancel' },
                        { text: 'Mở cài đặt', onPress: () => Linking.openSettings() },
                    ]
                );
            }
        });
    };

    // Hàm yêu cầu quyền camera
    const requestCameraPermission = async () => {
        try {
            const permission =
                Platform.OS === 'ios'
                    ? PERMISSIONS.IOS.CAMERA
                    : PERMISSIONS.ANDROID.CAMERA;

            const result = await request(permission);

            if (result === RESULTS.GRANTED) {
                setPermissionStatus('granted');
                Alert.alert('Thành công', 'Quyền truy cập camera đã được cấp.');
            } else if (result === RESULTS.DENIED) {
                setPermissionStatus('denied');
                Alert.alert('Quyền bị từ chối', 'Bạn đã từ chối quyền truy cập camera. Vui lòng cấp quyền trong cài đặt.');
            } else if (result === RESULTS.BLOCKED) {
                setPermissionStatus('blocked');
                Alert.alert(
                    'Quyền bị chặn',
                    'Quyền truy cập camera đã bị chặn. Vui lòng vào cài đặt để cấp quyền.',
                    [
                        { text: 'Hủy', style: 'cancel' },
                        { text: 'Mở cài đặt', onPress: () => Linking.openSettings() },
                    ]
                );
            } else {
                setPermissionStatus('unavailable');
                Alert.alert('Quyền không khả dụng', 'Quyền này không khả dụng trên thiết bị của bạn.');
            }
        } catch (error: any) {
            console.log(error);
            setPermissionStatus('error');
            Alert.alert('Lỗi', 'Đã có lỗi xảy ra khi yêu cầu quyền camera: ' + error.message);
        }
    };

    // Hàm chụp ảnh
    const takePhoto = async () => {
        if (!cameraRef.current) {
            Alert.alert('Lỗi', 'Camera không khả dụng.');
            return;
        }

        try {
            const photo = await cameraRef.current.takePhoto();
            setCapturedPhoto(`file://${photo.path}`);
        } catch (error: any) {
            console.log(error);
            Alert.alert('Lỗi', 'Không thể chụp ảnh: ' + error.message);
        }
    };

    // Kiểm tra quyền khi component được mount
    useEffect(() => {
        checkCameraPermission();
    }, []);

    // Kiểm tra nếu không có thiết bị camera
    if (!device) {
        return (
            <View style={styles.container}>
                <Text style={styles.title}>Không tìm thấy thiết bị camera</Text>
            </View>
        );
    }

    return (
        <View style={styles.container}>
            {permissionStatus === 'granted' ? (
                <>
                    {capturedPhoto ? (
                        <Image source={{ uri: capturedPhoto }} style={styles.preview} />
                    ) : (
                        <Camera
                            ref={cameraRef}
                            style={styles.camera}
                            device={device}
                            isActive={true}
                            photo={true} // Kích hoạt tính năng chụp ảnh
                        />
                    )}
                    <Button title="Chụp ảnh" onPress={takePhoto} />
                    {capturedPhoto && (
                        <Button
                            title="Chụp lại"
                            onPress={() => setCapturedPhoto(null)}
                        />
                    )}
                </>
            ) : (
                <>
                    <Text style={styles.title}>Yêu cầu quyền truy cập camera</Text>
                    <Text style={styles.status}>Trạng thái: {permissionStatus}</Text>
                    {permissionStatus === 'denied' && (
                        <Button title="Yêu cầu lại quyền camera" onPress={requestCameraPermission} />
                    )}
                    {permissionStatus === 'blocked' && (
                        <Button title="Mở cài đặt để cấp quyền" onPress={() => Linking.openSettings()} />
                    )}
                </>
            )}
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: 20,
        backgroundColor: '#f5f5f5',
        justifyContent: 'center',
        alignItems: 'center',
    },
    title: {
        fontSize: 20,
        fontWeight: 'bold',
        marginBottom: 20,
        color: '#2d4150',
    },
    status: {
        fontSize: 16,
        marginBottom: 20,
        color: '#2d4150',
    },
    camera: {
        width: '100%',
        height: 400,
        marginBottom: 20,
    },
    preview: {
        width: '100%',
        height: 400,
        marginBottom: 20,
    },
});

export default CameraPermission;
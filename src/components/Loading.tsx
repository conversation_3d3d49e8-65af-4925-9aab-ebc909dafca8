import React from 'react';
import { ActivityIndicator, StyleSheet, View } from 'react-native';
import { COLORS, ZINDEX } from '@/constants/UI/themes';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';

const Loading = () => {
    const isLoading = useSelector((state: RootState) => state.loading.isLoading);

    if (!isLoading) return null;

    return (
        <View style={StyleSheet.absoluteFillObject}>
            <View style={styles.container}>
                <ActivityIndicator size="large" color={COLORS.light.primary} />
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: COLORS.light.blurPopup,
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: ZINDEX.loading,
        elevation: ZINDEX.loading, // Cho Android
        opacity: 0.5,
    },
});

export default Loading;
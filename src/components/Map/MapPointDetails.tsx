import React from 'react';
import { View, StyleSheet, TouchableOpacity, Linking, Platform } from 'react-native';
import { useTheme } from '@react-navigation/native';
import CText from '../CText';
import CIcon from '../CIcon';
import { PointLocation } from './MapView';
import { Icustomer } from '@/constants/UI/icons';

interface MapPointDetailsProps {
    point: PointLocation;
    distance?: number; // Distance in meters
    duration?: number; // Time in seconds
    prevPointName?: string; // Name of previous point (for multi-map)
}

/**
 * Component to display details of a point on the map
 */
const MapPointDetails: React.FC<MapPointDetailsProps> = ({
    point,
    distance,
    duration,
    prevPointName,
}) => {
    const { colors } = useTheme();

    // Format distance
    const formatDistance = (meters: number): string => {
        if (meters < 1000) {
            return `${Math.round(meters)} m`;
        }
        return `${(meters / 1000).toFixed(1)} km`;
    };

    // Format duration
    const formatDuration = (seconds: number): string => {
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);

        if (hours > 0) {
            const remainingMinutes = minutes % 60;
            return `${hours} hours ${remainingMinutes} minutes`;
        }

        return `${minutes} minutes`;
    };

    // Call customer
    const handleCall = () => {
        if (point.customer?.phone) {
            const phoneNumber = Platform.OS === 'android'
                ? `tel:${point.customer.phone}`
                : `telprompt:${point.customer.phone}`;
            Linking.canOpenURL(phoneNumber)
                .then(supported => {
                    if (supported) {
                        Linking.openURL(phoneNumber);
                    } else {
                        console.log('Cannot make the call');
                    }
                })
                .catch(err => console.error('Error making call:', err));
        }
    };

    return (
        <View style={[styles.container, { backgroundColor: colors.card }]}>
            {/* Customer name and call button */}
            <View style={styles.header}>
                <View style={styles.customerInfo}>
                    <CText style={[styles.customerName, { color: colors.text }]}>
                        {point.customer?.name || 'No name'}
                    </CText>
                    {point.index !== undefined && (
                        <View style={[styles.indexBadge, { backgroundColor: colors.primary }]}>
                            <CText style={styles.indexText}>{point.index + 1}</CText>
                        </View>
                    )}
                </View>

                {point.customer?.phone && (
                    <TouchableOpacity
                        style={[styles.callButton, { backgroundColor: colors.primary }]}
                        onPress={handleCall}
                    >
                        <CIcon source={Icustomer} size={18} style={{ tintColor: 'white' }} />
                        <CText style={styles.callButtonText}>Call</CText>
                    </TouchableOpacity>
                )}
            </View>

            {/* Address */}
            <View style={styles.addressContainer}>
                <CText style={[styles.addressLabel, { color: colors.text }]}>
                    Address:
                </CText>
                <CText style={[styles.addressText, { color: colors.text }]}>
                    {point.address || 'No address'}
                </CText>
            </View>

            {/* Distance and time information (if available) */}
            {distance !== undefined && duration !== undefined && (
                <View style={styles.routeInfo}>
                    <View style={styles.routeDetail}>
                        <CText style={[styles.routeLabel, { color: colors.text }]}>
                            Distance:
                        </CText>
                        <CText style={[styles.routeValue, { color: colors.text }]}>
                            {formatDistance(distance)}
                        </CText>
                    </View>

                    <View style={styles.routeDetail}>
                        <CText style={[styles.routeLabel, { color: colors.text }]}>
                            Estimated time:
                        </CText>
                        <CText style={[styles.routeValue, { color: colors.text }]}>
                            {formatDuration(duration)}
                        </CText>
                    </View>

                    {prevPointName && (
                        <View style={styles.routeDetail}>
                            <CText style={[styles.routeLabel, { color: colors.text }]}>
                                From:
                            </CText>
                            <CText style={[styles.routeValue, { color: colors.text }]}>
                                {prevPointName}
                            </CText>
                        </View>
                    )}
                </View>
            )}

            {/* Task details (if available) */}
            {point.taskDetails && (
                <View style={styles.taskDetails}>
                    <CText style={[styles.taskTitle, { color: colors.text }]}>
                        Task details:
                    </CText>

                    {/* Display information based on taskDetails */}
                    {Object.entries(point.taskDetails || {}).map(([key, value]) =>
                        value ? (
                            <View key={key} style={styles.taskItem}>
                                <CText style={[styles.taskItemLabel, { color: colors.text }]}>
                                    {key}:
                                </CText>
                                <CText style={[styles.taskItemValue, { color: colors.text }]}>
                                    {String(value)}
                                </CText>
                            </View>
                        ) : null
                    )}
                </View>
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        padding: 16,
        borderRadius: 8,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 12,
    },
    customerInfo: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
    },
    customerName: {
        fontSize: 18,
        fontWeight: 'bold',
        marginRight: 8,
    },
    indexBadge: {
        width: 24,
        height: 24,
        borderRadius: 12,
        alignItems: 'center',
        justifyContent: 'center',
    },
    indexText: {
        color: 'white',
        fontSize: 12,
        fontWeight: 'bold',
    },
    callButton: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 6,
        paddingHorizontal: 12,
        borderRadius: 4,
    },
    callButtonText: {
        color: 'white',
        marginLeft: 4,
        fontWeight: '500',
    },
    addressContainer: {
        marginBottom: 12,
    },
    addressLabel: {
        fontSize: 14,
        fontWeight: '500',
        marginBottom: 4,
    },
    addressText: {
        fontSize: 14,
    },
    routeInfo: {
        marginTop: 8,
        marginBottom: 12,
        padding: 10,
        borderRadius: 6,
        backgroundColor: 'rgba(0,0,0,0.05)',
    },
    routeDetail: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 4,
    },
    routeLabel: {
        fontSize: 14,
        fontWeight: '500',
    },
    routeValue: {
        fontSize: 14,
    },
    taskDetails: {
        marginTop: 8,
    },
    taskTitle: {
        fontSize: 16,
        fontWeight: '500',
        marginBottom: 8,
    },
    taskItem: {
        flexDirection: 'row',
        marginBottom: 4,
    },
    taskItemLabel: {
        fontSize: 14,
        fontWeight: '500',
        marginRight: 4,
        width: 100,
    },
    taskItemValue: {
        fontSize: 14,
        flex: 1,
    }
});

export default MapPointDetails; 
import React from 'react';
import { View, StyleSheet, Image, TouchableOpacity } from 'react-native';
import { Controller, Control, FieldValues, Path, FieldErrors } from 'react-hook-form';
import { useTheme } from '@react-navigation/native';
import CText from '@/components/CText';
import ImagePicker from './ImagePicker';
import { FONT_WEIGHT } from '@/constants/UI/themes';

interface ImagePickerFieldProps<T extends FieldValues> {
    name: Path<T>;
    control: Control<T>;
    errors?: FieldErrors<T>;
    label?: string;
    required?: boolean;
    multiple?: boolean;
    maxImages?: number;
}

function ImagePickerField<T extends FieldValues>({
    name,
    control,
    errors,
    label,
    required = false,
    multiple = false,
    maxImages = 5,
}: ImagePickerFieldProps<T>) {
    const { colors } = useTheme() as any;

    return (
        <View style={styles.container}>
            {label && (
                <View style={styles.labelContainer}>
                    <CText style={[styles.label, { color: colors.text }]}>
                        {label}
                        {required && <CText style={{ color: '#FE5F5F' }}> *</CText>}
                    </CText>
                </View>
            )}

            <Controller
                control={control}
                name={name}
                render={({ field: { onChange, value } }) => {
                    // Ensure value is always an array
                    const images = Array.isArray(value) ? value : (value ? [value] : []);

                    // Handle image removal
                    const handleRemoveImage = (index: number) => {
                        const newImages = [...images];
                        newImages.splice(index, 1);
                        onChange(multiple ? newImages : newImages[0] || null);
                    };

                    // Handle when new images are selected
                    const handleImagesSelected = (selectedImages: string[]) => {
                        if (multiple) {
                            // If selecting multiple images, add to the current list
                            const newImages = [...images, ...selectedImages];
                            // Ensure not exceeding the allowed limit
                            onChange(newImages.slice(0, maxImages));
                        } else {
                            // If selecting a single image, replace the current image
                            onChange(selectedImages[0] || null);
                        }
                    };

                    return (
                        <View>
                            <ImagePicker
                                multiple={multiple}
                                maxImages={maxImages - (multiple ? images.length : 0)}
                                onImagesSelected={handleImagesSelected}
                            />

                            {/* Display selected images */}
                            {images.length > 0 && (
                                <View style={styles.imagesContainer}>
                                    {images.map((uri, index) => (
                                        <View key={index} style={styles.imageWrapper}>
                                            <Image source={{ uri }} style={styles.image} />
                                            <TouchableOpacity
                                                style={[styles.removeButton, { backgroundColor: colors.primary }]}
                                                onPress={() => handleRemoveImage(index)}
                                            >
                                                <CText style={styles.removeButtonText}>✕</CText>
                                            </TouchableOpacity>
                                        </View>
                                    ))}
                                </View>
                            )}
                        </View>
                    );
                }}
            />

            {/* Display error message if any */}
            {errors && errors[name] && (
                <CText style={[styles.errorText, { color: '#FE5F5F' }]}>
                    {errors[name]?.message as string}
                </CText>
            )}
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        marginBottom: 16,
    },
    labelContainer: {
        marginBottom: 8,
    },
    label: {
        fontSize: 16,
        fontWeight: FONT_WEIGHT.medium,
    },
    imagesContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        marginTop: 12,
    },
    imageWrapper: {
        position: 'relative',
        margin: 4,
    },
    image: {
        width: 100,
        height: 100,
        borderRadius: 8,
    },
    removeButton: {
        position: 'absolute',
        top: -8,
        right: -8,
        width: 24,
        height: 24,
        borderRadius: 12,
        alignItems: 'center',
        justifyContent: 'center',
    },
    removeButtonText: {
        color: 'white',
        fontSize: 12,
        fontWeight: FONT_WEIGHT.bold,
    },
    errorText: {
        fontSize: 14,
        marginTop: 4,
    },
});

export default ImagePickerField; 
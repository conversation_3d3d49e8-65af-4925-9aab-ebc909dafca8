import React from 'react';
import { Image, StyleSheet, View, ImageSourcePropType, Text } from 'react-native';
import { COLORS } from '@/constants/UI/themes';
interface ProfileAvatarProps {
    source?: ImageSourcePropType;
    name?: string;
    style?: object;
}

const ProfileAvatar: React.FC<ProfileAvatarProps> = ({ source, name, style }) => {
    const getInitials = (name?: string) => {
        if (!name) return '';
        return name
            .split(' ')
            .map(word => word.charAt(0))
            .join('')
            .toUpperCase()
            .slice(0, 2);
    };

    return (
        <View style={[styles.container, style]}>
            <Text style={styles.initials}>
                {/* {getInitials(name)} */}
                ML
            </Text>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        width: 65,
        height: 65,
        borderRadius: 32.5, // Half of width/height to make it perfectly circular
        overflow: 'hidden',
        justifyContent: 'center',
        alignItems: 'center',
    },
    image: {
        width: '100%',
        height: '100%',
    },
    initials: {
        fontSize: 22,
        fontWeight: '400',
        color: COLORS.light.primary, // <PERSON><PERSON>u chữ tối cho chữ cái
    },
});

export default ProfileAvatar; 
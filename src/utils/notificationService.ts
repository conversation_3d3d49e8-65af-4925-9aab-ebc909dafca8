// import messaging, { FirebaseMessagingTypes } from '@react-native-firebase/messaging';
// import { Platform } from 'react-native';
// import AsyncStorage from '@react-native-async-storage/async-storage';

// export async function requestUserPermission() {
//     if (Platform.OS === 'ios') {
//         const authStatus = await messaging().requestPermission();
//         const enabled =
//             authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
//             authStatus === messaging.AuthorizationStatus.PROVISIONAL;

//         if (enabled) {
//             console.log('Authorization status:', authStatus);
//             getFCMToken();
//             return true;
//         } else {
//             console.log('User denied permission');
//             return false;
//         }
//     }
//     return true;
// }


// const getFCMToken = async () => {
//     try {

//         const fcmToken = await AsyncStorage.getItem('fcmToken');


//         if (!fcmToken) {
//             const newToken = await messaging().getToken();
//             if (newToken) {
//                 console.log('New FCM Token:', newToken);
//                 await AsyncStorage.setItem('fcmToken', newToken);
//             }
//         } else {
//             console.log('Old FCM Token:', fcmToken);
//         }
//     } catch (error) {
//         console.log('Error getting/setting FCM token:', error);
//     }
// };


// export const notificationListeners = () => {

//     const unsubscribeForeground = messaging().onMessage(async (remoteMessage: FirebaseMessagingTypes.RemoteMessage) => {
//         console.log('Notification in foreground:', remoteMessage);

//     });


//     messaging().onNotificationOpenedApp((remoteMessage: FirebaseMessagingTypes.RemoteMessage) => {
//         console.log('Notification opened app:', remoteMessage);

//     });


//     messaging()
//         .getInitialNotification()
//         .then((remoteMessage: FirebaseMessagingTypes.RemoteMessage | null) => {
//             if (remoteMessage) {
//                 console.log('App opened by notification:', remoteMessage);

//             }
//         });


//     messaging().onTokenRefresh((token: string) => {
//         AsyncStorage.setItem('fcmToken', token);
//         console.log('FCM token refreshed:', token);

//     });


//     return unsubscribeForeground;
// };

// export default {
//     requestUserPermission,
//     notificationListeners,
//     getFCMToken
// }; 